package com.yelink.dfscommon.entity.dfs;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yelink.dfscommon.annotation.LogTag;
import com.yelink.dfscommon.common.unit.config.UnitColumn;
import com.yelink.dfscommon.common.unit.config.UnitFormatColumn;
import com.yelink.dfscommon.common.validate.FieldValueValidate;
import com.yelink.dfscommon.dto.dfs.PushDownIdentifierInfoDTO;
import com.yelink.dfscommon.entity.wms.StockInventoryDetailEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021-12-20 20:01
 */
@EqualsAndHashCode(callSuper = false)
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TableName("dfs_work_order_material_list_material")
@LogTag(bean = "workOrderMaterialListMaterialImpl", method = "getById", param = "id")
public class WorkOrderMaterialListMaterialEntity extends Model<WorkOrderMaterialListMaterialEntity> {

    /**
     * 用料清单关联物料id
     */
    @ApiModelProperty(value = "用料清单关联物料id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 生产工单用料清单id
     */
    @ApiModelProperty(value = "生产工单用料清单id")
    @TableField(value = "material_list_id")
    private Integer materialListId;

    /**
     * 生产工单用料清单编号
     */
    @ApiModelProperty(value = "生产工单用料清单编号")
    @TableField(value = "material_list_code")
    private String materialListCode;

    /**
     * 生产工单用料清单计划数量
     */
    @ApiModelProperty(value = "生产工单用料清单计划数量", required = true)
    @TableField(value = "material_list_plan_quantity")
    @UnitFormatColumn
    private BigDecimal materialListPlanQuantity;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码", required = true)
    @TableField(value = "material_code")
    @UnitColumn
    private String materialCode;

    /**
     * ERP关联物料行id
     */
    @ApiModelProperty("ERP关联物料行id")
    @TableField(value = "external_material_id")
    private String externalMaterialId;

    /**
     * bom分子
     */
    @ApiModelProperty("bom分子")
    @TableField(value = "bom_numerator")
    @UnitFormatColumn
    private BigDecimal bomNumerator = BigDecimal.ONE;

    /**
     * bom分母
     */
    @ApiModelProperty("bom分母")
    @TableField(value = "bom_denominator")
    @UnitFormatColumn
    private BigDecimal bomDenominator = BigDecimal.ONE;

    /**
     * 计划数量
     */
    @ApiModelProperty(value = "计划数量", required = true)
    @TableField(value = "plan_quantity")
    @UnitFormatColumn
    private BigDecimal planQuantity;

    /**
     * 实际数量
     */
    @ApiModelProperty(value = "实际数量")
    @TableField(value = "actual_quantity")
    @UnitFormatColumn
    private BigDecimal actualQuantity;

    /**
     * 未领数量
     */
    @ApiModelProperty(value = "未领数量")
    @TableField(exist = false)
    @UnitFormatColumn
    private BigDecimal uncollectedQuantity;

    /**
     * 参考数量
     */
    @ApiModelProperty(value = "参考数量")
    @TableField(value = "reference_quantity")
    @UnitFormatColumn
    private BigDecimal referenceQuantity;

    /**
     * 累计计划数量: 标准件计划数量 + 替代料的替代数量之和，替代料不存在此字段
     */
    @ApiModelProperty(value = "累计计划数量")
    @TableField(exist = false)
    @UnitFormatColumn
    private BigDecimal totalPlanQuantity;

    /**
     * 累计领用数量: 标准件实际数量 + （替代料的实际用料*主替用量比）之和，替代料不存在此字段
     */
    @ApiModelProperty(value = "累计领用数量")
    @TableField(exist = false)
    @UnitFormatColumn
    private BigDecimal totalReceiveQuantity;

    /**
     * 累计补料出库数
     */
    @ApiModelProperty(value = "累计补料出库数")
    @TableField(value = "total_material_issued")
    @UnitFormatColumn
    private BigDecimal totalMaterialIssued;

//    /**
//     * 领料数量
//     */
//    @ApiModelProperty(value = "领料数量")
//    @TableField(value = "take_out_quantity")
//    @UnitFormatColumn
//    private BigDecimal takeOutQuantity;

    /**
     * 退料数量
     */
    @ApiModelProperty("退料数量")
    @TableField(value = "return_quantity")
    @UnitFormatColumn
    private BigDecimal returnQuantity;

    /**
     * 已下推数量
     */
    @ApiModelProperty(value = "已下推数量")
    @TableField(value = "push_down_quantity")
    @UnitFormatColumn
    private BigDecimal pushDownQuantity;

    /**
     * 物料字段
     */
    @TableField(exist = false)
    private MaterialEntity materialFields;

    /**
     * 预估损耗率
     */
    @TableField(exist = false)
    private Double lossRate;

    /**
     * 固定损耗
     */
    @TableField(exist = false)
    private Double fixedDamage;

    /**
     * 子项类型(1001-标准件  1002-替代件)
     */
    @TableField(value = "sub_type")
    private Integer subType;

    /**
     * 子项类型名称(1001-标准件  1002-替代件)
     */
    @TableField(exist = false)
    private String subTypeName;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(value = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 替代数量
     */
    @ApiModelProperty(value = "替代数量")
    @TableField(value = "replace_quantity")
    @UnitFormatColumn
    private BigDecimal replaceQuantity;

    /**
     * 主物料编号
     */
    @TableField(value = "main_material_code")
    private String mainMaterialCode;

    /**
     * 可供选择的替代物id（多个则按逗号分隔)
     */
    @ApiModelProperty("可供选择的替代物id（多个则按逗号分隔）")
    @TableField(value = "replace_material_id")
    @FieldValueValidate(regex = "\\d+")
    private String replaceMaterialId;

    /**
     * 已选择的替代物id（多个则按逗号分隔)
     */
    @ApiModelProperty("已选择的替代物id（多个则按逗号分隔）")
    @TableField(value = "selected_replace_material_id")
    @FieldValueValidate(regex = "\\d+")
    private String selectedReplaceMaterialId;

    /**
     * 关联的替代方案替代物id（仅替代物才有)
     */
    @TableField(exist = false)
    private Integer relatedReplaceMaterialId;

    /**
     * 特征参数skuId
     */
    @ApiModelProperty("特征参数skuId")
    @TableField(value = "sku_id")
    private Integer skuId;

    /**
     * 已选择的替代物物料编码（多个则按逗号分隔）
     */
    @ApiModelProperty("已选择的替代物物料编码（多个则按逗号分隔）")
    @TableField(exist = false)
    private String selectedReplaceMaterialCode;

    /**
     * 物料当前总库存
     */
    @ApiModelProperty("物料当前总库存")
    @TableField(exist = false)
    private BigDecimal stockQuantity;

    /**
     * BOM子物料id
     */
    @ApiModelProperty("BOM子物料id")
    @TableField(value = "bom_raw_material_id")
    private Integer bomRawMaterialId;

    /**
     * BOM子物料拓展字段
     */
    @ApiModelProperty("BOM子物料拓展字段")
    @TableField(value = "bom_raw_material_extend_one")
    private String bomRawMaterialExtendOne;

    /**
     * 业务主体编码
     */
    @ApiModelProperty("业务主体编码")
    @TableField(value = "business_unit_code")
    private String businessUnitCode;
    /**
     * 业务主体名称
     */
    @ApiModelProperty("业务主体名称")
    @TableField(value = "business_unit_name")
    private String businessUnitName;

    /**
     * 生产工单用料清单物料扩展字段1
     */
    @ApiModelProperty(value = "生产工单用料清单物料扩展字段1")
    @TableField(value = "material_list_material_extend_field_one")
    private String materialListMaterialExtendFieldOne;
    /**
     * 生产工单用料清单物料扩展字段2
     */
    @ApiModelProperty(value = "生产工单用料清单物料扩展字段2")
    @TableField(value = "material_list_material_extend_field_two")
    private String materialListMaterialExtendFieldTwo;
    /**
     * 生产工单用料清单物料扩展字段3
     */
    @ApiModelProperty(value = "生产工单用料清单物料扩展字段3")
    @TableField(value = "material_list_material_extend_field_three")
    private String materialListMaterialExtendFieldThree;
    /**
     * 生产工单用料清单物料扩展字段4
     */
    @ApiModelProperty(value = "生产工单用料清单物料扩展字段4")
    @TableField(value = "material_list_material_extend_field_four")
    private String materialListMaterialExtendFieldFour;
    /**
     * 生产工单用料清单物料扩展字段5
     */
    @ApiModelProperty(value = "生产工单用料清单物料扩展字段5")
    @TableField(value = "material_list_material_extend_field_five")
    private String materialListMaterialExtendFieldFive;

    /**
     * 生产工单用料清单物料扩展字段6
     */
    @ApiModelProperty(value = "生产工单用料清单物料扩展字段6")
    @TableField(value = "material_list_material_extend_field_six")
    private String materialListMaterialExtendFieldSix;
    /**
     * 生产工单用料清单物料扩展字段7
     */
    @ApiModelProperty(value = "生产工单用料清单物料扩展字段7")
    @TableField(value = "material_list_material_extend_field_seven")
    private String materialListMaterialExtendFieldSeven;
    /**
     * 生产工单用料清单物料扩展字段8
     */
    @ApiModelProperty(value = "生产工单用料清单物料扩展字段8")
    @TableField(value = "material_list_material_extend_field_eight")
    private String materialListMaterialExtendFieldEight;
    /**
     * 生产工单用料清单物料扩展字段9
     */
    @ApiModelProperty(value = "生产工单用料清单物料扩展字段9")
    @TableField(value = "material_list_material_extend_field_nine")
    private String materialListMaterialExtendFieldNine;
    /**
     * 生产工单用料清单物料扩展字段10
     */
    @ApiModelProperty(value = "生产工单用料清单物料扩展字段10")
    @TableField(value = "material_list_material_extend_field_ten")
    private String materialListMaterialExtendFieldTen;

    /**
     * 物料关联仓库
     */
    @TableField(exist = false)
    private List<StockInventoryDetailEntity> stockInventoryDetailEntities;

    /**
     * 扩展字段中文名
     */
    @TableField(exist = false)
    private String materialListMaterialExtendFieldOneName;
    @TableField(exist = false)
    private String materialListMaterialExtendFieldTwoName;
    @TableField(exist = false)
    private String materialListMaterialExtendFieldThreeName;
    @TableField(exist = false)
    private String materialListMaterialExtendFieldFourName;
    @TableField(exist = false)
    private String materialListMaterialExtendFieldFiveName;
    @TableField(exist = false)
    private String materialListMaterialExtendFieldSixName;
    @TableField(exist = false)
    private String materialListMaterialExtendFieldSevenName;
    @TableField(exist = false)
    private String materialListMaterialExtendFieldEightName;
    @TableField(exist = false)
    private String materialListMaterialExtendFieldNineName;
    @TableField(exist = false)
    private String materialListMaterialExtendFieldTenName;

    /**
     * 父id
     */
    @TableField(exist = false)
    private Integer parentId;

    /**
     * 下推标识信息列表
     */
    @ApiModelProperty("下推标识信息列表")
    @TableField(exist = false)
    private List<PushDownIdentifierInfoDTO> pushDownIdentifierInfos;

    @Override
    public Serializable pkVal() {
        return this.materialListId;
    }

}
