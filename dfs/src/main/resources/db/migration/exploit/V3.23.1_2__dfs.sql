-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.23.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.23.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.23.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.23.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.23.1.1=======================================================21.2
-- 客户档案
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', NULL, 'customer', '客户档案', 'customer', 'dfs', 1, NULL);
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', NULL, 'customerList', '客户档案表页', 'customer.list', 'customer', 1, 'list');
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', NULL, 'customerDetail', '客户档案详情页', 'customer.detail', 'customer', 1, 'detail');
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', NULL, 'customerEdit', '客户档案编辑页', 'customer.edit', 'customer', 1, NULL);
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', 0, 'customerEditByCreate', '创建态', 'customer.edit.byCreate', 'customer.edit', 1, 'edit-create');
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', 0, 'customerEditByRelease', '生效态', 'customer.edit.byRelease', 'customer.edit', 1, 'edit-other');
INSERT INTO `dfs_form_config` (`type`, `is_name_tree`, `code`, `name`, `full_path_code`, `parent_full_path_code`, `is_inner`, `module`) VALUES ('web', 0, 'customerEditByDisable', '停用态', 'customer.edit.byDisable', 'customer.edit', 1, 'edit-other');

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('customer.list', 'customer.list', 'baseField', '单据基本信息', 'base', '基础字段', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('customer.detail', 'customer.detail', 'baseField', '单据基本信息', 'base', '基础字段', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('customer.edit.byCreate', 'customer.edit.byCreate', 'baseField', '单据基本信息', 'base', '基础字段', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('customer.edit.byRelease', 'customer.edit.byRelease', 'baseField', '单据基本信息', 'base', '基础字段', 1, 0, 1);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('customer.edit.byDisable', 'customer.edit.byDisable', 'baseField', '单据基本信息', 'base', '基础字段', 1, 0, 1);

INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('customer.list', 'customer.list', 'baseExtendField', '单据基本信息扩展字段', 'extend', '扩展字段', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('customer.detail', 'customer.detail', 'baseExtendField', '单据基本信息扩展字段', 'extend', '扩展字段', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('customer.edit.byRelease', 'customer.edit.byRelease', 'baseExtendField', '单据基本信息扩展字段', 'extend', '扩展字段', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('customer.edit.byDisable', 'customer.edit.byDisable', 'baseExtendField', '单据基本信息扩展字段', 'extend', '扩展字段', 1, 0, 2);
INSERT INTO `dfs_form_field_module_config`(`full_path_code`, `field_name_full_path_code`, `module_code`, `module_name`, `parent_module_code`, `parent_module_name`, `have_save_button`, `have_add_button`, `sort`) VALUES ('customer.edit.byCreate', 'customer.edit.byCreate', 'baseExtendField', '单据基本信息扩展字段', 'extend', '扩展字段', 1, 0, 2);


-- 列表
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'customerCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'customerName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'customerContacts', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'customerMobile', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'customerPriority', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'salesman', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'salesmanNickName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'salesmanMobile', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'stateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'companyType', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'companyScale', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'remark', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'updateTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'createByName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'updateByName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'customerExtendFieldOne', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'customerExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'customerExtendFieldThree', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'customerExtendFieldFour', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'customerExtendFieldFive', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'customerExtendFieldSix', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'customerExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'customerExtendFieldEight', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'customerExtendFieldNine', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.list', 'customer.list', 'customerExtendFieldTen', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');

call proc_add_form_field('customer.list', 'customerCode', '客户编码');
call proc_add_form_field('customer.list', 'customerName', '客户名称');
call proc_add_form_field('customer.list', 'customerContacts', '客户联系人');
call proc_add_form_field('customer.list', 'customerMobile', '客户联系方式');
call proc_add_form_field('customer.list', 'customerPriority', '客户优先级');
call proc_add_form_field('customer.list', 'salesman', '业务员');
call proc_add_form_field('customer.list', 'salesmanNickName', '业务员简称');
call proc_add_form_field('customer.list', 'salesmanMobile', '业务员联系方式');
call proc_add_form_field('customer.list', 'stateName', '状态');
call proc_add_form_field('customer.list', 'companyType', '公司类型');
call proc_add_form_field('customer.list', 'companyScale', '公司规模');
call proc_add_form_field('customer.list', 'remark', '备注');
call proc_add_form_field('customer.list', 'createTime', '创建时间');
call proc_add_form_field('customer.list', 'updateTime', '修改时间');
call proc_add_form_field('customer.list', 'createByName', '创建人');
call proc_add_form_field('customer.list', 'updateByName', '修改人');

call proc_add_form_field_module('customer.list', 'customerExtendFieldOne', '客户扩展字段1', 'baseExtendField');
call proc_add_form_field_module('customer.list', 'customerExtendFieldTwo', '客户扩展字段2', 'baseExtendField');
call proc_add_form_field_module('customer.list', 'customerExtendFieldThree', '客户扩展字段3', 'baseExtendField');
call proc_add_form_field_module('customer.list', 'customerExtendFieldFour', '客户扩展字段4', 'baseExtendField');
call proc_add_form_field_module('customer.list', 'customerExtendFieldFive', '客户扩展字段5', 'baseExtendField');
call proc_add_form_field_module('customer.list', 'customerExtendFieldSix', '客户扩展字段6', 'baseExtendField');
call proc_add_form_field_module('customer.list', 'customerExtendFieldSeven', '客户扩展字段7', 'baseExtendField');
call proc_add_form_field_module('customer.list', 'customerExtendFieldEight', '客户扩展字段8', 'baseExtendField');
call proc_add_form_field_module('customer.list', 'customerExtendFieldNine', '客户扩展字段9', 'baseExtendField');
call proc_add_form_field_module('customer.list', 'customerExtendFieldTen', '客户扩展字段10', 'baseExtendField');

-- 详情
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'customerCode', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'customerName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'customerContacts', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'customerMobile', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'customerPriority', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'salesman', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'salesmanNickName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'salesmanMobile', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'stateName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'companyType', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'companyScale', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'remark', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'updateTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'createByName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'updateByName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'customerExtendFieldOne', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'customerExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'customerExtendFieldThree', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'customerExtendFieldFour', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'customerExtendFieldFive', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'customerExtendFieldSix', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'customerExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'customerExtendFieldEight', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'customerExtendFieldNine', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.detail', 'customer.list', 'customerExtendFieldTen', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');

call proc_add_form_field('customer.detail', 'customerCode', '客户编码');
call proc_add_form_field('customer.detail', 'customerName', '客户名称');
call proc_add_form_field('customer.detail', 'customerContacts', '客户联系人');
call proc_add_form_field('customer.detail', 'customerMobile', '客户联系方式');
call proc_add_form_field('customer.detail', 'customerPriority', '客户优先级');
call proc_add_form_field('customer.detail', 'salesman', '业务员');
call proc_add_form_field('customer.detail', 'salesmanNickName', '业务员简称');
call proc_add_form_field('customer.detail', 'salesmanMobile', '业务员联系方式');
call proc_add_form_field('customer.detail', 'stateName', '状态');
call proc_add_form_field('customer.detail', 'companyType', '公司类型');
call proc_add_form_field('customer.detail', 'companyScale', '公司规模');
call proc_add_form_field('customer.detail', 'remark', '备注');
call proc_add_form_field('customer.detail', 'createTime', '创建时间');
call proc_add_form_field('customer.detail', 'updateTime', '修改时间');
call proc_add_form_field('customer.detail', 'createByName', '创建人');
call proc_add_form_field('customer.detail', 'updateByName', '修改人');

call proc_add_form_field_module('customer.detail', 'customerExtendFieldOne', '客户扩展字段1', 'baseExtendField');
call proc_add_form_field_module('customer.detail', 'customerExtendFieldTwo', '客户扩展字段2', 'baseExtendField');
call proc_add_form_field_module('customer.detail', 'customerExtendFieldThree', '客户扩展字段3', 'baseExtendField');
call proc_add_form_field_module('customer.detail', 'customerExtendFieldFour', '客户扩展字段4', 'baseExtendField');
call proc_add_form_field_module('customer.detail', 'customerExtendFieldFive', '客户扩展字段5', 'baseExtendField');
call proc_add_form_field_module('customer.detail', 'customerExtendFieldSix', '客户扩展字段6', 'baseExtendField');
call proc_add_form_field_module('customer.detail', 'customerExtendFieldSeven', '客户扩展字段7', 'baseExtendField');
call proc_add_form_field_module('customer.detail', 'customerExtendFieldEight', '客户扩展字段8', 'baseExtendField');
call proc_add_form_field_module('customer.detail', 'customerExtendFieldNine', '客户扩展字段9', 'baseExtendField');
call proc_add_form_field_module('customer.detail', 'customerExtendFieldTen', '客户扩展字段10', 'baseExtendField');

-- 编辑(创建态)
call proc_add_form_field('customer.edit', 'customerCode', '客户编码');
call proc_add_form_field('customer.edit', 'customerName', '客户名称');
call proc_add_form_field('customer.edit', 'customerContacts', '客户联系人');
call proc_add_form_field('customer.edit', 'customerMobile', '客户联系方式');
call proc_add_form_field('customer.edit', 'customerPriority', '客户优先级');
call proc_add_form_field('customer.edit', 'salesman', '业务员');
call proc_add_form_field('customer.edit', 'salesmanNickName', '业务员简称');
call proc_add_form_field('customer.edit', 'salesmanMobile', '业务员联系方式');
call proc_add_form_field('customer.edit', 'state', '状态');
call proc_add_form_field('customer.edit', 'companyType', '公司类型');
call proc_add_form_field('customer.edit', 'companyScale', '公司规模');
call proc_add_form_field('customer.edit', 'remark', '备注');
call proc_add_form_field('customer.edit', 'createTime', '创建时间');
call proc_add_form_field('customer.edit', 'updateTime', '修改时间');
call proc_add_form_field('customer.edit', 'createBy', '创建人');
call proc_add_form_field('customer.edit', 'updateBy', '修改人');

call proc_add_form_field_module('customer.edit', 'customerExtendFieldOne', '客户扩展字段1', 'baseExtendField');
call proc_add_form_field_module('customer.edit', 'customerExtendFieldTwo', '客户扩展字段2', 'baseExtendField');
call proc_add_form_field_module('customer.edit', 'customerExtendFieldThree', '客户扩展字段3', 'baseExtendField');
call proc_add_form_field_module('customer.edit', 'customerExtendFieldFour', '客户扩展字段4', 'baseExtendField');
call proc_add_form_field_module('customer.edit', 'customerExtendFieldFive', '客户扩展字段5', 'baseExtendField');
call proc_add_form_field_module('customer.edit', 'customerExtendFieldSix', '客户扩展字段6', 'baseExtendField');
call proc_add_form_field_module('customer.edit', 'customerExtendFieldSeven', '客户扩展字段7', 'baseExtendField');
call proc_add_form_field_module('customer.edit', 'customerExtendFieldEight', '客户扩展字段8', 'baseExtendField');
call proc_add_form_field_module('customer.edit', 'customerExtendFieldNine', '客户扩展字段9', 'baseExtendField');
call proc_add_form_field_module('customer.edit', 'customerExtendFieldTen', '客户扩展字段10', 'baseExtendField');

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'customerCode', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'customerName', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'customerContacts', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'customerMobile', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'customerPriority', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'salesman', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'salesmanNickName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'salesmanMobile', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'state', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 0, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'companyType', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'companyScale', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'remark', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'updateTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'createBy', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'updateBy', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'customerExtendFieldOne', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'customerExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'customerExtendFieldThree', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'customerExtendFieldFour', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'customerExtendFieldFive', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'customerExtendFieldSix', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'customerExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'customerExtendFieldEight', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'customerExtendFieldNine', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byCreate', 'customer.edit', 'customerExtendFieldTen', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');

-- 编辑（生效）
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'customerCode', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'customerName', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'customerContacts', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'customerMobile', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'customerPriority', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'salesman', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'salesmanNickName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'salesmanMobile', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'state', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 0, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'companyType', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'companyScale', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'remark', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'updateTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'createBy', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'updateBy', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'customerExtendFieldOne', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'customerExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'customerExtendFieldThree', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'customerExtendFieldFour', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'customerExtendFieldFive', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'customerExtendFieldSix', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'customerExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'customerExtendFieldEight', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'customerExtendFieldNine', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byRelease', 'customer.edit', 'customerExtendFieldTen', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');

-- 编辑（停用）
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'customerCode', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'customerName', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'customerContacts', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'customerMobile', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'customerPriority', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'salesman', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'salesmanNickName', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'salesmanMobile', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'state', 1, 1, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 0, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'companyType', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'companyScale', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'remark', 1, 0, 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'createTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'updateTime', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'createBy', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'updateBy', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1);

INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'customerExtendFieldOne', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'customerExtendFieldTwo', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'customerExtendFieldThree', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'customerExtendFieldFour', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'customerExtendFieldFive', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'customerExtendFieldSix', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'customerExtendFieldSeven', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'customerExtendFieldEight', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'customerExtendFieldNine', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, `module_code`) VALUES ('/supply-chain-collaboration/order-model/clientFile', 'customer.edit.byDisable', 'customer.edit', 'customerExtendFieldTen', 0, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 1, 0, 1, 1, 1, NULL, NULL, 1, 'baseExtendField');

-- 新增客户档案二开接口查询脚本
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'customerId', '客户id', 'customer_id');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'customerCode', '客户编号', 'customer_code');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'customerName', '客户名称', 'customer_name');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'customerMobile', '客户联系方式', 'customer_mobile');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'salesman', '业务员', 'salesman');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'cusState', '客户状态', 'cus_state');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'createTime', '创建时间', 'create_time');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'updateTime', '更新时间', 'update_time');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'createBy', '创建人', 'create_by');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'updateBy', '修改人', 'update_by');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'customerContacts', '客户联系人', 'customer_contacts');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'dutyParagraph', '纳税人识别号', 'duty_paragraph');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'bankAccount', '开户行', 'bank_account');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'bankNumber', '账号', 'bank_number');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'companyNumber', '电话', 'company_number');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'companyAddress', '注册地址', 'company_address');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'remark', '备注', 'remark');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'customerPriority', '客户优先级', 'customer_priority');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'companyType', '公司类型', 'company_type');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'companyScale', '公司规模', 'company_scale');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'companyFullName', '企业全称', 'company_full_name');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'customerExtendFieldOne', '客户档案拓展字段1', 'customer_extend_field_one');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'customerExtendFieldTwo', '客户档案拓展字段2', 'customer_extend_field_two');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'customerExtendFieldThree', '客户档案拓展字段3', 'customer_extend_field_three');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'customerExtendFieldFour', '客户档案拓展字段4', 'customer_extend_field_four');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'customerExtendFieldFive', '客户档案拓展字段5', 'customer_extend_field_five');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'customerExtendFieldSix', '客户档案拓展字段6', 'customer_extend_field_six');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'customerExtendFieldSeven', '客户档案拓展字段7', 'customer_extend_field_seven');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'customerExtendFieldEight', '客户档案拓展字段8', 'customer_extend_field_eight');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'customerExtendFieldNine', '客户档案拓展字段9', 'customer_extend_field_nine');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customer', 'customerExtendFieldTen', '客户档案拓展字段10', 'customer_extend_field_ten');


INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'customerId', '客户档案拓展字段10', 'id');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'customerCode', '客户编号', 'customer_code');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'state', '客户物料状态', 'state');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'materialCode', '物料编码', 'material_code');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'externalMaterialId', 'ERP关联物料行id', 'external_material_id');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'customerMaterialCode', '客户物料编码', 'customer_material_code');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'customerMaterialName', '客户物料名称', 'customer_material_name');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'remark', '备注', 'remark');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'updateBy', '更新人', 'update_by');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'createBy', '创建人', 'create_by');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'updateTime', '更新时间', 'update_time');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'createTime', '创建时间', 'create_time');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'customerMaterialStandard', '客户物料规格', 'customer_material_standard');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'price', '价格', 'price');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'customerMaterialExtendFieldOne', '客户物料扩展字段1', 'customer_material_extend_field_one');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'customerMaterialExtendFieldTwo', '客户物料扩展字段2', 'customer_material_extend_field_two');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'customerMaterialExtendFieldThree', '客户物料扩展字段3', 'customer_material_extend_field_three');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'customerMaterialExtendFieldFour', '客户物料扩展字段4', 'customer_material_extend_field_four');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'customerMaterialExtendFieldFive', '客户物料扩展字段5', 'customer_material_extend_field_five');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'customerMaterialExtendFieldSix', '客户物料扩展字段6', 'customer_material_extend_field_six');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'customerMaterialExtendFieldSeven', '客户物料扩展字段7', 'customer_material_extend_field_seven');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'customerMaterialExtendFieldEight', '客户物料扩展字段8', 'customer_material_extend_field_eight');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'customerMaterialExtendFieldNine', '客户物料扩展字段9', 'customer_material_extend_field_nine');
INSERT INTO `dfs_query_field_config`(`prefix`, `field_code`, `field_name`, `table_field_code`) VALUES ('customerMaterial', 'customerMaterialExtendFieldTen', '客户物料扩展字段10', 'customer_material_extend_field_ten');


-- 创建单据下推标识表
CREATE TABLE IF NOT EXISTS `dfs_order_push_down_identifier`
(
    `id`                   int NOT NULL AUTO_INCREMENT COMMENT 'id',
    `order_type`           varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '源单据类型',
    `order_material_id`    varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '单据物料行id',
    `batch_number`         varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '批次号',
--     `value_full_path_code` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '软件参数全路径编码',
    `target_order_type`    varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '目标单据类型',
    `state`                varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '下推标识状态(noPushDown-未下推 partPushDown-部分下推 allPushDown-已下推)',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                    `unique_key` (`order_type`,`order_material_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='单据下推标识表';


-- 下推标识软参设置
INSERT INTO `dfs_business_config`(`code`, `name`, `full_path_code`, `parent_full_path_code`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `service_name`) VALUES ('pushDownIdentifierConf', '下推标识配置', 'globalConf.pushDownIdentifierConf', 'globalConf', '用于标记单据下推状态的颜色', 'yelinkoncall', 'yelinkoncall', '2023-10-10 12:12:48', '2024-09-20 14:24:57', '');
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('noPushDown', '未下推', 'globalConf.pushDownIdentifierConf.noPushDown.colorIdentifier', 'globalConf.pushDownIdentifierConf', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '\"#ED5851\"', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('partPushDown', '部分下推', 'globalConf.pushDownIdentifierConf.partPushDown.colorIdentifier', 'globalConf.pushDownIdentifierConf', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '\"#FAF85F\"', NULL);
INSERT INTO `dfs_business_config_value`(`value_code`, `value_name`, `value_full_path_code`, `config_full_path_code`, `input_type`, `option_values_type`, `url`, `method`, `params`, `resp_param_field`, `option_values`, `value`, `rules`) VALUES ('allPushDown', '已下推', 'globalConf.pushDownIdentifierConf.allPushDown.colorIdentifier', 'globalConf.pushDownIdentifierConf', 'color', 'table', NULL, NULL, NULL, NULL, NULL, '\"#81FA70\"', NULL);


-- 新增下推标识字段
call proc_add_form_field_module('workOrder.list', 'pushDownIdentifier', '下推标识', 'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/order-model/production-workorder', 'workOrder.list', 'workOrder.list', 'pushDownIdentifier', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');

call proc_add_form_field_module('workOrderMaterialList.list.material', 'pushDownIdentifier', '下推标识', 'baseField');
INSERT INTO `dfs_form_field_rule_config` (`route`, `full_path_code`, `field_name_full_path_code`, `field_code`, `is_show`, `is_need`, `is_edit`, `is_scan`, `input_type`, `option_values_type`, `related_api_code`, `url`, `resp_param_field`, `all_option`, `option_values`, `default_value`, `rules`, `show_gray`, `need_gray`, `edit_gray`, `input_gray`, `value_gray`, `default_value_gray`, `rules_gray`, `scan_gray`, `remark`, `formula`, `sort`, module_code) VALUES ('/order-model/workOrder-materials', 'workOrderMaterialList.list.material', 'workOrderMaterialList.list.material', 'pushDownIdentifier', 1, 0, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, 1, 1, 1, 1, NULL, NULL, 1,'baseField');
-- 重命名软参名称
UPDATE `dfs_business_config` SET `name` = '生产工单用料清单配置' WHERE `full_path_code` = 'production.workerMaterialsList.listConfig';

-- 字段配置的列表展示需要
UPDATE `dfs_form_field_rule_config` t1
    JOIN `dfs_form_field_rule_config` t2
ON t1.`field_code` = t2.`field_code`
    AND t2.`full_path_code` = 'material.editByCreate'
    AND t2.`is_show` = 1
    AND t2.`show_gray` = 1
    SET t1.`is_show` = 1, t1.`show_gray` = 1
WHERE t1.`full_path_code` in ('material.list', 'material.detail');

UPDATE `dfs_form_field_rule_config` t1
    JOIN `dfs_form_field_rule_config` t2
ON t1.`field_code` = t2.`field_code`
    AND t2.`full_path_code` = 'supplier.edit.byCreate'
    AND t2.`is_show` = 1
    AND t2.`show_gray` = 1
    SET t1.`is_show` = 1, t1.`show_gray` = 1
WHERE t1.`full_path_code` in ('supplier.list', 'supplier.detail');

UPDATE `dfs_form_field_rule_config` t1
    JOIN `dfs_form_field_rule_config` t2
ON t1.`field_code` = t2.`field_code`
    AND t2.`full_path_code` = 'supplierMaterial.edit'
    AND t2.`is_show` = 1
    AND t2.`show_gray` = 1
    SET t1.`is_show` = 1, t1.`show_gray` = 1
WHERE t1.`full_path_code` = 'supplierMaterial.list';

UPDATE `dfs_form_field_rule_config` t1
    JOIN `dfs_form_field_rule_config` t2
ON t1.`field_code` = t2.`field_code`
    AND t2.`full_path_code` = 'customer.edit.byCreate'
    AND t2.`is_show` = 1
    AND t2.`show_gray` = 1
    SET t1.`is_show` = 1, t1.`show_gray` = 1
WHERE t1.`full_path_code` in ('customer.list', 'customer.detail');

UPDATE `dfs_form_field_rule_config` t1
    JOIN `dfs_form_field_rule_config` t2
ON t1.`field_code` = t2.`field_code`
    AND t2.`full_path_code` = 'device.add'
    AND t2.`is_show` = 1
    AND t2.`show_gray` = 1
    SET t1.`is_show` = 1, t1.`show_gray` = 1
WHERE t1.`full_path_code` in ('device.list', 'device.detail');

UPDATE `dfs_form_field_rule_config` t1
    JOIN `dfs_form_field_rule_config` t2
ON t1.`field_code` = t2.`field_code`
    AND t2.`full_path_code` = 'deviceInspectionPlanItem.edit.byCreate'
    AND t2.`is_show` = 1
    AND t2.`show_gray` = 1
    SET t1.`is_show` = 1, t1.`show_gray` = 1
WHERE t1.`full_path_code` in ('deviceInspectionPlanItem.list', 'deviceInspectionPlanItem.detail');


-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.23.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.23.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.23.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.23.1.1=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到3.23.1.1=======================================================21.2

-- ================================================刷新任务表历史数据的orderId字段=======================================================
-- 刷新工单任务的orderId字段
UPDATE dfs_task t
INNER JOIN dfs_work_order w ON t.order_category = 'workOrder' AND t.order_number = w.work_order_number
SET t.order_id = CAST(w.work_order_id AS CHAR)
WHERE t.order_category = 'workOrder' AND (t.order_id IS NULL OR t.order_id = '');

-- 刷新工单用料清单任务的orderId字段
UPDATE dfs_task t
INNER JOIN dfs_work_order_material_list wml ON t.order_category = 'workOrderMaterialList' AND t.order_number = wml.material_list_code
SET t.order_id = CAST(wml.material_list_id AS CHAR)
WHERE t.order_category = 'workOrderMaterialList' AND (t.order_id IS NULL OR t.order_id = '');
