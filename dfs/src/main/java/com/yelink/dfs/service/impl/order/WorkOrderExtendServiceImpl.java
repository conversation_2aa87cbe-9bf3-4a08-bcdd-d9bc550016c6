package com.yelink.dfs.service.impl.order;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.dfs.common.CodeFactory;
import com.yelink.dfs.common.UserAuthenService;
import com.yelink.dfs.constant.AppendixTypeEnum;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.device.DevicesStateEnum;
import com.yelink.dfs.constant.model.BusinessUnitStateEnum;
import com.yelink.dfs.constant.order.AssignmentStateEnum;
import com.yelink.dfs.constant.order.OrderMergeStateEnum;
import com.yelink.dfs.constant.order.PriorityTypeEnum;
import com.yelink.dfs.constant.product.ProductionStateEnum;
import com.yelink.dfs.constant.rule.AutoIncrementConfigureTypeEnum;
import com.yelink.dfs.constant.task.TaskStateEnum;
import com.yelink.dfs.entity.alarm.AlarmEntity;
import com.yelink.dfs.entity.attendance.AttendanceEntity;
import com.yelink.dfs.entity.barcode.BarCodeEntity;
import com.yelink.dfs.entity.capacity.dto.ProductionBasicUnitSelectDTO;
import com.yelink.dfs.entity.capacity.dto.ProductionBasicUnitTypeDTO;
import com.yelink.dfs.entity.capacity.dto.ProductionBasicUnitTypeSelectDTO;
import com.yelink.dfs.entity.code.ProductFlowCodeEntity;
import com.yelink.dfs.entity.code.ProductFlowCodeRecordEntity;
import com.yelink.dfs.entity.common.DeleteRecordEntity;
import com.yelink.dfs.entity.common.OrderChangeLogEntity;
import com.yelink.dfs.entity.common.config.dto.WorkOrderAssignmentConfigDTO;
import com.yelink.dfs.entity.common.config.dto.WorkOrderStateChangeConfDTO;
import com.yelink.dfs.entity.defect.DefectRecordEntity;
import com.yelink.dfs.entity.device.DeviceCalendarEntity;
import com.yelink.dfs.entity.device.DeviceEntity;
import com.yelink.dfs.entity.event.EventRecordEntity;
import com.yelink.dfs.entity.feed.FeedRecordEntity;
import com.yelink.dfs.entity.furnace.FurnaceEntity;
import com.yelink.dfs.entity.furnace.RefiningFurnaceRecordEntity;
import com.yelink.dfs.entity.iot.WorkOrderBarcodeEntity;
import com.yelink.dfs.entity.maintain.MaintainRecordEntity;
import com.yelink.dfs.entity.management.PackageRecordEntity;
import com.yelink.dfs.entity.manufacture.FacUserReportEntity;
import com.yelink.dfs.entity.manufacture.FacWorkOrderEntity;
import com.yelink.dfs.entity.model.LineEmployeeEntity;
import com.yelink.dfs.entity.model.ModelEntity;
import com.yelink.dfs.entity.model.WorkCenterDeviceEntity;
import com.yelink.dfs.entity.model.WorkCenterDeviceRelevanceEntity;
import com.yelink.dfs.entity.model.WorkCenterLineRelevanceEntity;
import com.yelink.dfs.entity.model.WorkCenterTeamEntity;
import com.yelink.dfs.entity.model.WorkCenterTeamRelevanceEntity;
import com.yelink.dfs.entity.model.dto.BusinessUnitSelectDTO;
import com.yelink.dfs.entity.model.dto.WorkCenterSelectDTO;
import com.yelink.dfs.entity.model.vo.BusinessUnitVO;
import com.yelink.dfs.entity.order.CustomerEntity;
import com.yelink.dfs.entity.order.FacInputRecordEntity;
import com.yelink.dfs.entity.order.InputRecordEntity;
import com.yelink.dfs.entity.order.OrderExecuteSeqEntity;
import com.yelink.dfs.entity.order.OrderWorkOrderEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderCountEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderDayCountEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderLineDayCountEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderMaterialPlanEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderUnqualifiedEntity;
import com.yelink.dfs.entity.order.StaffPieceWorkTimeEntity;
import com.yelink.dfs.entity.order.TakeOutApplicationEntity;
import com.yelink.dfs.entity.order.WorkOrderBasicUnitInputRecordEntity;
import com.yelink.dfs.entity.order.WorkOrderBasicUnitRelationEntity;
import com.yelink.dfs.entity.order.WorkOrderDeviceRelevanceEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.WorkOrderFileEntity;
import com.yelink.dfs.entity.order.WorkOrderFlowEntity;
import com.yelink.dfs.entity.order.WorkOrderInputEntity;
import com.yelink.dfs.entity.order.WorkOrderInvestCheckResultEntity;
import com.yelink.dfs.entity.order.WorkOrderLineRelevanceEntity;
import com.yelink.dfs.entity.order.WorkOrderMaterialCheckMaterialEntity;
import com.yelink.dfs.entity.order.WorkOrderOperatorEntity;
import com.yelink.dfs.entity.order.WorkOrderPlanEntity;
import com.yelink.dfs.entity.order.WorkOrderProcedureRelationEntity;
import com.yelink.dfs.entity.order.WorkOrderProductLineRelationEntity;
import com.yelink.dfs.entity.order.WorkOrderTeamEntity;
import com.yelink.dfs.entity.order.WorkOrderTeamRelevanceEntity;
import com.yelink.dfs.entity.order.dto.WorkOrderExcelDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderSmartUpdateDTO;
import com.yelink.dfs.entity.order.tmp.TmpMaterialReadinessInspectionEntity;
import com.yelink.dfs.entity.order.vo.OrderTraceGroupVO;
import com.yelink.dfs.entity.order.vo.OrderTraceVO;
import com.yelink.dfs.entity.pack.PackageBoxEntity;
import com.yelink.dfs.entity.pack.PackageSchemeEntity;
import com.yelink.dfs.entity.product.BomEntity;
import com.yelink.dfs.entity.product.BomRawMaterialEntity;
import com.yelink.dfs.entity.product.CraftProcedureEntity;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.entity.product.ProcedureFileEntity;
import com.yelink.dfs.entity.product.ProcedureMaterialEntity;
import com.yelink.dfs.entity.product.dto.WorkOrderProcedureDTO;
import com.yelink.dfs.entity.quality.QualityEntity;
import com.yelink.dfs.entity.shift.ShiftCountEntity;
import com.yelink.dfs.entity.target.metrics.MetricsValuationCalEntity;
import com.yelink.dfs.entity.target.metrics.MetricsWorkOrderDailyEntity;
import com.yelink.dfs.entity.target.metrics.MetricsWorkOrderEntity;
import com.yelink.dfs.entity.target.metrics.MetricsWorkOrderHourlyEntity;
import com.yelink.dfs.entity.target.record.RecordDeviceDayRunEntity;
import com.yelink.dfs.entity.target.record.RecordDeviceDayUnionEntity;
import com.yelink.dfs.entity.target.record.RecordDeviceOrderUnionEntity;
import com.yelink.dfs.entity.target.record.RecordDeviceResumeDailyEntity;
import com.yelink.dfs.entity.target.record.RecordLineInputOutputOrderEntity;
import com.yelink.dfs.entity.target.record.RecordLineInputOutputOrderHourEntity;
import com.yelink.dfs.entity.target.record.RecordManualCollectionEntity;
import com.yelink.dfs.entity.target.record.RecordWorkOrderProgressEntity;
import com.yelink.dfs.entity.target.record.RecordWorkOrderStateEntity;
import com.yelink.dfs.entity.target.record.ReportCountEntity;
import com.yelink.dfs.entity.target.record.ReportDayCountEntity;
import com.yelink.dfs.entity.target.record.ReportDeviceOrderFinishEntity;
import com.yelink.dfs.entity.target.record.ReportLineEntity;
import com.yelink.dfs.entity.task.TaskEntity;
import com.yelink.dfs.entity.task.TaskLogEntity;
import com.yelink.dfs.entity.task.TaskUserConfigEntity;
import com.yelink.dfs.entity.task.dto.TaskDeleteDTO;
import com.yelink.dfs.entity.task.dto.TaskRelationUpsertDTO;
import com.yelink.dfs.entity.task.dto.TaskUpsertDTO;
import com.yelink.dfs.entity.task.dto.WorkOrderTaskDTO;
import com.yelink.dfs.entity.task.vo.TaskUserVO;
import com.yelink.dfs.entity.user.SysRoleEntity;
import com.yelink.dfs.mapper.barcode.BarCodeMapper;
import com.yelink.dfs.mapper.capacity.CapacityMapper;
import com.yelink.dfs.mapper.code.ProductFlowCodeRecordMapper;
import com.yelink.dfs.mapper.manufacture.ProductionLineMapper;
import com.yelink.dfs.mapper.model.WorkCenterMapper;
import com.yelink.dfs.mapper.order.RecordWorkOrderCountMapper;
import com.yelink.dfs.mapper.order.RecordWorkOrderDayCountMapper;
import com.yelink.dfs.mapper.order.WorkOrderMapper;
import com.yelink.dfs.mapper.order.WorkOrderPlanMapper;
import com.yelink.dfs.mapper.order.WorkOrderProductLineRelationMapper;
import com.yelink.dfs.mapper.product.CraftProcedureInspectControllerMapper;
import com.yelink.dfs.mapper.target.record.RecordManualCollectionMapper;
import com.yelink.dfs.mapper.target.record.ReportCountMapper;
import com.yelink.dfs.mapper.target.record.ReportLineMapper;
import com.yelink.dfs.mapper.terminal.SysPadMapper;
import com.yelink.dfs.mapper.user.SysRoleMapper;
import com.yelink.dfs.open.v1.aksk.ams.ExtProductOrderInterface;
import com.yelink.dfs.open.v1.aksk.ams.ExtSaleOrderInterface;
import com.yelink.dfs.open.v1.production.dto.BomMaterialSelectDTO;
import com.yelink.dfs.open.v1.workOrder.dto.WorkOrderBasicUnitInvestSelectDTO;
import com.yelink.dfs.open.v1.workOrder.dto.WorkOrderNumberDTO;
import com.yelink.dfs.open.v1.workOrder.dto.WorkOrderNumbersDTO;
import com.yelink.dfs.open.v1.workOrder.vo.WorkOrderCraftVO;
import com.yelink.dfs.open.v2.material.vo.MaterialFieldVO;
import com.yelink.dfs.open.v2.order.vo.WorkOrderBasicUnitRelationVO;
import com.yelink.dfs.provider.FastDfsClientService;
import com.yelink.dfs.provider.constant.KafkaMessageTypeEnum;
import com.yelink.dfs.service.alarm.AlarmService;
import com.yelink.dfs.service.approve.config.ApproveConfigService;
import com.yelink.dfs.service.attendance.AttendanceService;
import com.yelink.dfs.service.code.ProductFlowCodeService;
import com.yelink.dfs.service.common.AppendixService;
import com.yelink.dfs.service.common.CommonService;
import com.yelink.dfs.service.common.DeleteRecordService;
import com.yelink.dfs.service.common.ImportDataRecordService;
import com.yelink.dfs.service.common.OperationLogService;
import com.yelink.dfs.service.common.OrderChangeLogService;
import com.yelink.dfs.service.common.UploadService;
import com.yelink.dfs.service.common.config.BusinessConfigService;
import com.yelink.dfs.service.common.config.BusinessConfigValueService;
import com.yelink.dfs.service.common.config.FieldMappingService;
import com.yelink.dfs.service.common.config.OrderPushDownConfigService;
import com.yelink.dfs.service.common.config.OrderPushDownRecordService;
import com.yelink.dfs.service.common.config.OrderTypeConfigService;
import com.yelink.dfs.service.defect.DefectRecordService;
import com.yelink.dfs.service.device.DeviceCalendarService;
import com.yelink.dfs.service.device.DeviceService;
import com.yelink.dfs.service.event.EventRecordService;
import com.yelink.dfs.service.feed.FeedRecordService;
import com.yelink.dfs.service.furnace.FurnaceService;
import com.yelink.dfs.service.furnace.RefiningFurnaceRecordService;
import com.yelink.dfs.service.impl.common.WorkPropertise;
import com.yelink.dfs.service.iot.WorkOrderBarcodeService;
import com.yelink.dfs.service.maintain.MaintainRecordService;
import com.yelink.dfs.service.management.PackageRecordService;
import com.yelink.dfs.service.manufacture.FacUserReportService;
import com.yelink.dfs.service.manufacture.FacWorkOrderService;
import com.yelink.dfs.service.manufacture.ProductionLineService;
import com.yelink.dfs.service.model.BusinessUnitService;
import com.yelink.dfs.service.model.LineEmployeeService;
import com.yelink.dfs.service.model.ModelService;
import com.yelink.dfs.service.model.WorkCenterDeviceRelevanceService;
import com.yelink.dfs.service.model.WorkCenterDeviceService;
import com.yelink.dfs.service.model.WorkCenterLineRelevanceService;
import com.yelink.dfs.service.model.WorkCenterService;
import com.yelink.dfs.service.model.WorkCenterTeamRelevanceService;
import com.yelink.dfs.service.model.WorkCenterTeamService;
import com.yelink.dfs.service.order.CustomerService;
import com.yelink.dfs.service.order.FacInputRecordService;
import com.yelink.dfs.service.order.InputRecordService;
import com.yelink.dfs.service.order.OrderExecuteSeqService;
import com.yelink.dfs.service.order.OrderWorkOrderService;
import com.yelink.dfs.service.order.RecordWorkOrderCountService;
import com.yelink.dfs.service.order.RecordWorkOrderDayCountService;
import com.yelink.dfs.service.order.RecordWorkOrderLineDayCountService;
import com.yelink.dfs.service.order.RecordWorkOrderMaterialPlanService;
import com.yelink.dfs.service.order.RecordWorkOrderUnqualifiedService;
import com.yelink.dfs.service.order.RuleSeqService;
import com.yelink.dfs.service.order.StaffPieceWorkTimeService;
import com.yelink.dfs.service.order.TakeOutApplicationService;
import com.yelink.dfs.service.order.TmpMaterialReadinessInspectionService;
import com.yelink.dfs.service.order.WorkOrderBasicUnitInputRecordService;
import com.yelink.dfs.service.order.WorkOrderBasicUnitRelationService;
import com.yelink.dfs.service.order.WorkOrderDeviceRelevanceService;
import com.yelink.dfs.service.order.WorkOrderExtendService;
import com.yelink.dfs.service.order.WorkOrderFileService;
import com.yelink.dfs.service.order.WorkOrderFlowService;
import com.yelink.dfs.service.order.WorkOrderInputService;
import com.yelink.dfs.service.order.WorkOrderInvestCheckResultService;
import com.yelink.dfs.service.order.WorkOrderLineRelevanceService;
import com.yelink.dfs.service.order.WorkOrderMaterialCheckMaterialService;
import com.yelink.dfs.service.order.WorkOrderMaterialListMaterialService;
import com.yelink.dfs.service.order.WorkOrderMaterialListService;
import com.yelink.dfs.service.order.WorkOrderOperatorService;
import com.yelink.dfs.service.order.WorkOrderPlanService;
import com.yelink.dfs.service.order.WorkOrderProcedureRelationService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.order.WorkOrderTeamRelevanceService;
import com.yelink.dfs.service.order.WorkOrderTeamService;
import com.yelink.dfs.service.pack.PackageBoxService;
import com.yelink.dfs.service.pack.PackageSchemeService;
import com.yelink.dfs.service.product.BomService;
import com.yelink.dfs.service.product.CraftProcedureService;
import com.yelink.dfs.service.product.CraftService;
import com.yelink.dfs.service.product.MaterialAuxiliaryAttrService;
import com.yelink.dfs.service.product.MaterialInspectMethodService;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.product.ProcedureFileService;
import com.yelink.dfs.service.product.ProcedureMaterialService;
import com.yelink.dfs.service.product.ProcedureService;
import com.yelink.dfs.service.product.SkuService;
import com.yelink.dfs.service.quality.QualityService;
import com.yelink.dfs.service.rule.NumberRuleService;
import com.yelink.dfs.service.shift.ShiftCountService;
import com.yelink.dfs.service.sys.SysService;
import com.yelink.dfs.service.target.metrics.MetricsValuationCalService;
import com.yelink.dfs.service.target.metrics.MetricsWorkOrderDailyService;
import com.yelink.dfs.service.target.metrics.MetricsWorkOrderHourlyService;
import com.yelink.dfs.service.target.metrics.MetricsWorkOrderService;
import com.yelink.dfs.service.target.record.RecordDeviceDayRunService;
import com.yelink.dfs.service.target.record.RecordDeviceDayUnionService;
import com.yelink.dfs.service.target.record.RecordDeviceOrderUnionService;
import com.yelink.dfs.service.target.record.RecordDeviceResumeDailyService;
import com.yelink.dfs.service.target.record.RecordLineInputOutputOrderHourService;
import com.yelink.dfs.service.target.record.RecordLineInputOutputOrderService;
import com.yelink.dfs.service.target.record.RecordWorkOrderProgressService;
import com.yelink.dfs.service.target.record.RecordWorkOrderStateService;
import com.yelink.dfs.service.target.record.ReportCountService;
import com.yelink.dfs.service.target.record.ReportDayCountService;
import com.yelink.dfs.service.target.record.ReportDeviceOrderFinishService;
import com.yelink.dfs.service.target.record.ReportLineService;
import com.yelink.dfs.service.task.TaskLogService;
import com.yelink.dfs.service.task.TaskService;
import com.yelink.dfs.service.task.TaskUserConfigService;
import com.yelink.dfs.service.user.SysRoleService;
import com.yelink.dfs.service.user.SysTeamService;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfs.service.user.TeamTypeDefService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfscommon.api.ams.ProductOrderDailyPlanInterface;
import com.yelink.dfscommon.api.assignment.AssignmentInterface;
import com.yelink.dfscommon.api.wms.StockInAndOutInterface;
import com.yelink.dfscommon.api.wms.StockInventoryDetailInterface;
import com.yelink.dfscommon.constant.BusinessTypeEnum;
import com.yelink.dfscommon.constant.CategoryTypeEnum;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.ModelEnum;
import com.yelink.dfscommon.constant.OperationType;
import com.yelink.dfscommon.constant.OrderNumTypeEnum;
import com.yelink.dfscommon.constant.RedisKeyPrefix;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.ams.WorkOrderMaterialListMaterialChooseEnum;
import com.yelink.dfscommon.constant.approve.config.ApprovalStatusEnum;
import com.yelink.dfscommon.constant.approve.config.ApproveModuleEnum;
import com.yelink.dfscommon.constant.dfs.CraftRelatedConditionEnum;
import com.yelink.dfscommon.constant.dfs.MaterialListRelateOrderEnum;
import com.yelink.dfscommon.constant.dfs.ModuleEnum;
import com.yelink.dfscommon.constant.dfs.OrderTypeShowTypeEnum;
import com.yelink.dfscommon.constant.dfs.OutsourcingProcedureEnum;
import com.yelink.dfscommon.constant.dfs.TaskRelationTypeEnum;
import com.yelink.dfscommon.constant.dfs.barcode.BarCodeTypeEnum;
import com.yelink.dfscommon.constant.dfs.code.ProductFlowCodeTypeEnum;
import com.yelink.dfscommon.constant.dfs.config.BomSplitTypeEnum;
import com.yelink.dfscommon.constant.dfs.config.ConfigConstant;
import com.yelink.dfscommon.constant.dfs.config.CraftSplitTypeEnum;
import com.yelink.dfscommon.constant.dfs.config.WorkOrderSplittingRulesTypeEnum;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderMaterialChooseEnum;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderStateEnum;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownInstanceTypeEnum;
import com.yelink.dfscommon.constant.dfs.rule.NumberCodeDTO;
import com.yelink.dfscommon.constant.model.WorkCenterTypeEnum;
import com.yelink.dfscommon.dto.CommonTableDTO;
import com.yelink.dfscommon.dto.MaterialCodeAndSkuIdSelectDTO;
import com.yelink.dfscommon.dto.ams.open.order.ProductOrderDetailDTO;
import com.yelink.dfscommon.dto.ams.open.order.SaleOrderDetailDTO;
import com.yelink.dfscommon.dto.ams.open.order.SaleOrderSelectOpenDTO;
import com.yelink.dfscommon.dto.ams.order.CraftProcedureMaterialListDTO;
import com.yelink.dfscommon.dto.ams.order.ProductOrderParamDTO;
import com.yelink.dfscommon.dto.common.config.BusinessTypeListVO;
import com.yelink.dfscommon.dto.common.config.FieldMappingSelectDTO;
import com.yelink.dfscommon.dto.common.config.FullPathCodeDTO;
import com.yelink.dfscommon.dto.common.config.OrderTypeInfoVO;
import com.yelink.dfscommon.dto.common.config.OrderTypeSelectDTO;
import com.yelink.dfscommon.dto.dfs.BomVO;
import com.yelink.dfscommon.dto.dfs.DefaultCraftPushDownDTO;
import com.yelink.dfscommon.dto.dfs.OrderMaterialListDTO;
import com.yelink.dfscommon.dto.dfs.RuleDetailDTO;
import com.yelink.dfscommon.dto.dfs.WorkOrderBasicUnitInsertDTO;
import com.yelink.dfscommon.dto.dfs.WorkOrderBasicUnitRelationInsertDTO;
import com.yelink.dfscommon.dto.dfs.WorkOrderOperatorDTO;
import com.yelink.dfscommon.dto.dfs.WorkOrderOperatorSelectDTO;
import com.yelink.dfscommon.dto.dfs.WorkOrderProductBasicUnitChangeDTO;
import com.yelink.dfscommon.dto.dfs.push.AbstractPushDTO;
import com.yelink.dfscommon.dto.dfs.push.CraftProcedureVO;
import com.yelink.dfscommon.dto.dfs.push.CraftRoutePushWorkOrderVO;
import com.yelink.dfscommon.dto.dfs.push.CraftVO;
import com.yelink.dfscommon.dto.dfs.push.DeviceVO;
import com.yelink.dfscommon.dto.dfs.push.ProductionLineVO;
import com.yelink.dfscommon.dto.dfs.push.SourceOrderPushDownMaterialListVO;
import com.yelink.dfscommon.dto.dfs.push.TeamVO;
import com.yelink.dfscommon.dto.pushdown.item.PushDownRecordDeleteDTO;
import com.yelink.dfscommon.dto.pushdown.item.PushDownRecordSelectDTO;
import com.yelink.dfscommon.entity.AppendixEntity;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.entity.ams.ProductOrderEntity;
import com.yelink.dfscommon.entity.ams.ProductOrderMaterialEntity;
import com.yelink.dfscommon.entity.ams.SaleOrderEntity;
import com.yelink.dfscommon.entity.ams.SaleOrderMaterialEntity;
import com.yelink.dfscommon.entity.ams.dto.ProductOrderPlanPushRecordAddDTO;
import com.yelink.dfscommon.entity.ams.dto.SaleOrderVO;
import com.yelink.dfscommon.entity.dfs.ActualPushDownConfigDTO;
import com.yelink.dfscommon.entity.dfs.CraftEntity;
import com.yelink.dfscommon.entity.dfs.FieldMappingEntity;
import com.yelink.dfscommon.entity.dfs.MaterialAuxiliaryAttrEntity;
import com.yelink.dfscommon.entity.dfs.OperationLogEntity;
import com.yelink.dfscommon.entity.dfs.OrderPushDownConfigEntity;
import com.yelink.dfscommon.entity.dfs.OrderPushDownRecordEntity;
import com.yelink.dfscommon.entity.dfs.ProductionLineEntity;
import com.yelink.dfscommon.entity.dfs.SysTeamEntity;
import com.yelink.dfscommon.entity.dfs.TeamTypeDefEntity;
import com.yelink.dfscommon.entity.dfs.WorkCenterEntity;
import com.yelink.dfscommon.entity.dfs.WorkOrderMaterialListEntity;
import com.yelink.dfscommon.entity.dfs.WorkOrderMaterialListMaterialEntity;
import com.yelink.dfscommon.provider.MessagePushToKafkaService;
import com.yelink.dfscommon.utils.ColumnUtil;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.MathUtil;
import com.yelink.dfscommon.utils.NullableDouble;
import com.yelink.dfscommon.utils.WrapperUtil;
import com.yelink.dfscommon.vo.pushdown.OrderMaterialListVO;
import com.yelink.dfscommon.vo.pushdown.OrderPushDownRuleItemVO;
import com.yelink.inner.service.KafkaWebSocketPublisher;
import com.yelink.inner.service.impl.kafka.task.TaskRegisterServiceImpl;
import com.yelink.sdk.task.constant.OrderCategoryEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class WorkOrderExtendServiceImpl extends ServiceImpl<WorkOrderMapper, WorkOrderEntity> implements WorkOrderExtendService {

    @Resource
    private MaterialService materialService;
    @Resource
    private ExtSaleOrderInterface extSaleOrderInterface;
    @Resource
    private ExtProductOrderInterface extProductOrderInterface;
    @Resource
    protected ProcedureService procedureService;
    @Resource
    @Lazy
    protected ProductionLineMapper productionLineMapper;
    @Resource
    @Lazy
    protected WorkCenterService workCenterService;
    @Resource
    @Lazy
    private WorkCenterDeviceService workCenterDeviceService;
    @Resource
    @Lazy
    private WorkCenterTeamService workCenterTeamService;
    @Resource
    private WorkCenterLineRelevanceService workCenterLineRelevanceService;
    @Resource
    private WorkCenterTeamRelevanceService workCenterTeamRelevanceService;
    @Resource
    private WorkCenterDeviceRelevanceService workCenterDeviceRelevanceService;
    @Resource
    protected OrderTypeConfigService orderTypeConfigService;
    @Resource
    protected DeviceCalendarService deviceCalendarService;
    @Resource
    @Lazy
    protected WorkOrderMaterialListService workOrderMaterialListService;
    @Resource
    @Lazy
    protected WorkOrderMaterialListMaterialService workOrderMaterialListMaterialService;
    @Resource
    protected CodeFactory codeFactory;
    @Resource
    protected OrderWorkOrderService orderWorkOrderService;
    @Resource
    private ProcedureMaterialService procedureMaterialService;
    @Resource
    protected WorkPropertise workPropertise;
    @Resource
    protected MaterialInspectMethodService materialInspectMethodService;
    @Resource
    protected StockInAndOutInterface inAndOutInterface;
    @Resource
    protected SysUserService userService;
    @Resource
    protected SysPadMapper sysPadMapper;
    @Resource
    protected SysRoleMapper sysRoleMapper;
    @Resource
    protected WorkOrderPlanMapper workOrderPlanMapper;
    @Resource
    protected WorkOrderPlanService workOrderPlanService;
    @Resource
    protected StockInventoryDetailInterface inventoryDetailInterface;
    @Resource
    protected CapacityMapper capacityMapper;
    @Resource
    protected RecordWorkOrderMaterialPlanService materialPlanService;
    @Resource
    protected RedisTemplate<String, Object> redisTemplate;
    @Resource
    protected RecordManualCollectionMapper recordManualCollectionMapper;
    @Resource
    protected RecordDeviceDayUnionService recordDeviceDayUnionService;
    @Resource
    protected RecordDeviceOrderUnionService recordDeviceOrderUnionService;
    @Resource
    protected RecordDeviceDayRunService recordDeviceDayRunService;
    @Resource
    protected ReportDeviceOrderFinishService reportDeviceOrderFinishService;
    @Resource
    protected ReportCountMapper reportCountMapper;
    @Resource
    protected ReportLineMapper reportLineMapper;
    @Resource
    protected RecordWorkOrderCountMapper recordWorkOrderCountMapper;
    @Resource
    protected RecordWorkOrderDayCountMapper recordWorkOrderDayCountMapper;
    @Resource
    protected RecordWorkOrderMaterialPlanService recordWorkOrderMaterialPlanService;
    @Resource
    protected ApproveConfigService approveConfigService;
    @Resource
    protected KafkaWebSocketPublisher kafkaWebSocketPublisher;
    @Resource
    protected FastDfsClientService fastDfsClientService;
    @Resource
    protected WorkOrderFileService workOrderFileService;
    @Resource
    protected UploadService uploadService;
    @Resource
    protected RecordWorkOrderStateService workOrderStateService;
    @Resource
    protected MessagePushToKafkaService messagePushToKafkaService;
    @Resource
    protected AssignmentInterface assignmentInterface;
    @Resource
    protected CraftService craftService;
    @Resource
    private CraftProcedureService craftProcedureService;
    @Resource
    protected CraftProcedureInspectControllerMapper craftProcedureInspectControllerMapper;
    @Resource
    protected ImportDataRecordService importDataRecordService;
    @Resource
    protected AppendixService appendixService;
    @Resource
    protected ProcedureFileService procedureFileService;
    @Resource
    protected WorkOrderProcedureRelationService workOrderProcedureRelationService;
    @Resource
    private ModelService modelService;
    @Resource
    private WorkOrderOperatorService workOrderOperatorService;
    @Resource
    private TeamTypeDefService teamTypeDefService;
    @Resource
    private ProductFlowCodeRecordMapper productFlowCodeRecordMapper;
    @Resource
    private WorkOrderProductLineRelationMapper workOrderProductLineRelationMapper;
    @Resource
    private WorkOrderTeamService workOrderTeamService;
    @Resource
    private WorkOrderDeviceRelevanceService workOrderDeviceRelevanceService;
    @Resource
    @Lazy
    private RecordDeviceResumeDailyService recordDeviceResumeDailyService;
    @Resource
    private WorkOrderTeamRelevanceService workOrderTeamRelevanceService;
    @Resource
    private WorkOrderLineRelevanceService workOrderLineRelevanceService;
    @Resource
    private OrderExecuteSeqService orderExecuteSeqService;
    @Resource
    private WorkOrderMaterialCheckMaterialService workOrderMaterialCheckMaterialService;
    @Resource
    protected SkuService skuService;
    @Resource
    private ProductFlowCodeService productFlowCodeService;
    @Resource
    private BarCodeMapper barCodeMapper;
    @Resource
    private OrderChangeLogService orderChangeLogService;
    @Resource
    private DeleteRecordService deleteRecordService;
    @Resource
    private WorkOrderInvestCheckResultService investCheckResultService;
    @Resource
    protected UserAuthenService userAuthenService;
    @Resource
    private BusinessConfigService businessConfigService;
    @Resource
    @Lazy
    private SysRoleService roleService;
    @Resource
    private OrderPushDownRecordService pushDownRecordService;
    @Resource
    @Lazy
    protected ProductionLineService productionLineService;
    @Resource
    @Lazy
    private WorkOrderBasicUnitRelationService basicUnitRelationService;
    @Resource
    private BusinessConfigValueService businessConfigValueService;
    @Resource
    private DeviceService deviceService;
    @Resource
    private SysTeamService teamService;
    @Resource
    @Lazy
    private WorkOrderBasicUnitInputRecordService basicUnitInputRecordService;
    @Resource
    private TaskService taskService;
    @Resource
    private TaskUserConfigService taskUserConfigService;
    @Resource
    private TaskLogService taskLogService;
    @Resource
    private OrderPushDownConfigService pushDownConfigService;
    @Resource
    private OperationLogService operationLogService;
    @Resource
    private CommonService commonService;
    @Resource
    private NumberRuleService numberRuleService;
    @Resource
    private RuleSeqService ruleSeqService;
    @Resource
    private FieldMappingService fieldMappingService;
    @Resource
    @Lazy
    private MaterialAuxiliaryAttrService materialAuxiliaryAttrService;
    @Resource
    private OrderPushDownRecordService orderPushDownRecordService;
    @Resource
    private ProductOrderDailyPlanInterface productOrderDailyPlanInterface;
    @Resource
    private BusinessUnitService businessUnitService;
    @Resource
    protected CustomerService customerService;
    @Resource
    private LineEmployeeService lineEmployeeService;
    @Resource
    protected SysTeamService sysTeamService;
    @Resource
    private WorkCenterMapper workCenterMapper;
    @Resource
    private PackageSchemeService packageSchemeService;
    @Resource
    protected WorkOrderMapper workOrderMapper;
    @Resource
    private TaskRegisterServiceImpl taskRegister;
    @Resource
    @Lazy
    private BomService bomService;

    /**
     * 关联的表全部删除，如果涉及其他微服务，发送kafka消息通知删除
     * (异步处理，防止删除的数据量过大导致前端接口超时)
     */
    @Async
    @Override
    public void removeAllRelatedDataByWorkOrder(WorkOrderEntity entity, String username) {
        // 除创建状态的工单，其他情况下删除工单时需要记录删除的数据，以便后续问题排查及数据恢复
        if (!entity.getState().equals(WorkOrderStateEnum.CREATED.getCode())) {
            recordDeleteRecordByWorkOrder(entity, username);
        }

        // 关联的表全部删除，如果涉及其他微服务，发送kafka消息通知删除
        // 工单附件需要同时将附件删除，注意删除的顺序不能乱
        removeFile(entity.getWorkOrderId());
        deleteRelatedDataWhenDeleteWorkOrder(entity);
        // 删除通过脚本找不到的表
        deleteOtherRelatedDataByWorkOrder(entity.getWorkOrderNumber());
        // 推送删除工单的消息，通知其他第三方删除了该工单
        messagePushToKafkaService.pushNewMessage(entity, Constants.KAFKA_VALUE_CHAIN_TOPIC, KafkaMessageTypeEnum.WORK_ORDER_DELETE_MESSAGE);
    }

    @Override
    public Boolean workOrderStateChange(String username) {
        List<SysRoleEntity> roleEntities = roleService.selectByUsername(username);
        if (CollectionUtils.isEmpty(roleEntities)) {
            return false;
        }
        FullPathCodeDTO dto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.WORK_ORDER_STATE_CHANGE_CONF).build();
        WorkOrderStateChangeConfDTO config = businessConfigService.getValueDto(dto, WorkOrderStateChangeConfDTO.class);
        if (!config.getEnable()) {
            return false;
        }
        List<String> roleNames = roleEntities.stream().map(SysRoleEntity::getName).collect(Collectors.toList());
        for (String roleName : config.getRoles()) {
            if (roleNames.contains(roleName)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public List<OrderMaterialListVO> getPushDownMaterialList(OrderMaterialListDTO dto) {
        // 如果不支持反选下推，则直接返回
        if (Boolean.FALSE.equals(dto.getIsSupportMaterialLineReversePushDown())) {
            return new ArrayList<>();
        }
        WorkOrderService workOrderService = SpringUtil.getBean(WorkOrderService.class);
        List<WorkOrderEntity> workOrderEntities = workOrderService.listByIds(dto.getOrderMaterialIds());
        // 获取物料列表
        Set<String> materialCodes = workOrderEntities.stream().map(WorkOrderEntity::getMaterialCode).collect(Collectors.toSet());
        List<MaterialEntity> materialEntities = materialService.lambdaQuery().in(MaterialEntity::getCode, materialCodes).list();
        Map<String, MaterialEntity> codeMaterialMap = materialEntities.stream().collect(Collectors.toMap(MaterialEntity::getCode, v -> v));
        // 获取下推记录列表
        PushDownRecordSelectDTO recordSelectDTO = PushDownRecordSelectDTO.builder()
                .sourceOrderType(dto.getSourceOrderType())
                .sourceOrderMaterialIds(dto.getOrderMaterialIds())
                .targetOrderType(dto.getTargetOrderType())
                .build();
        Page<OrderPushDownRecordEntity> recordPage = pushDownRecordService.getRecordList(recordSelectDTO);
        List<BusinessTypeListVO> businessTypeListVOS = orderTypeConfigService.getOrderTypeListByOrderCategory(OrderTypeSelectDTO.builder().categoryCode(CategoryTypeEnum.WORK_ORDER.getTypeCode()).build());
        Map<String, String> orderTypeMap = businessTypeListVOS.stream().flatMap(v -> v.getOrderTypeListVOList().stream()).collect(Collectors.toMap(BusinessTypeListVO.OrderTypeListVO::getOrderType, BusinessTypeListVO.OrderTypeListVO::getOrderTypeName));
        // 获取工单关联的工艺工序id
        List<String> workOrderNumbers = workOrderEntities.stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
        Map<String, String> craftProcedureIdMap = workOrderProcedureRelationService.getCraftProcedureIdMap(workOrderNumbers);

        Map<Integer, List<OrderPushDownRecordEntity>> map = recordPage.getRecords().stream().collect(Collectors.groupingBy(OrderPushDownRecordEntity::getSourceOrderMaterialId));
        List<OrderMaterialListVO> vos = workOrderEntities.stream().map(o -> {
                    List<OrderPushDownRecordEntity> recordEntities = map.getOrDefault(o.getWorkOrderId(), new ArrayList<>());
                    // 过滤下推标识一致的数据（下推标识一致的数据说明是同一次的源单据下推）
                    List<OrderPushDownRecordEntity> distinctRecordEntities = recordEntities.stream().filter(WrapperUtil.distinctByKey(OrderPushDownRecordEntity::getPushDownCode)).collect(Collectors.toList());
                    int pushDownTimes = distinctRecordEntities.size();
                    // 过滤不需要计算在下推数量的数据
                    double pushDownQuantitySum = distinctRecordEntities.stream().filter(record -> !record.getIsAbnormal()).mapToDouble(OrderPushDownRecordEntity::getPushDownQuantity).sum();
                    String pushDownStatusName = pushDownQuantitySum == 0 ? "未下推" : pushDownQuantitySum < o.getPlanQuantity() ? "部分下推" : "已下推";
                    boolean materialPushDown = Objects.nonNull(dto.getOrderMaterialIds()) && dto.getOrderMaterialIds().contains(o.getWorkOrderId());
                    // 根据下推配置判断获取工单物料BOM还是工序用料,如果是工序用料则不组装bom
                    ActualPushDownConfigDTO pushDownConfig = pushDownConfigService.getPushDownConfig(dto.getItemId());
                    BomEntity bomEntity = null;
                    if (!WorkOrderMaterialListMaterialChooseEnum.PROCEDURE_MATERIAL_USED.getCode().equals(pushDownConfig.getMaterialChoose())) {
                        BomMaterialSelectDTO bomMaterialSelectDTO = BomMaterialSelectDTO.builder()
                                .productionState(o.getBusinessType().equals(BusinessTypeEnum.TEST_WORK_ORDER.getTypeCode()) ? ProductionStateEnum.TEST_PRODUCTION.getCode() : ProductionStateEnum.MASS_PRODUCTION.getCode())
                                .materialCode(o.getMaterialCode())
                                .skuId(o.getSkuId())
                                .build();
                        bomEntity = bomService.getNewBom(bomMaterialSelectDTO);
                        // 找不到就找BOM模板
                        if (Objects.isNull(bomEntity)) {
                            BomMaterialSelectDTO bomTemplateMaterialSelectDTO = BomMaterialSelectDTO.builder()
                                    .productionState(o.getBusinessType().equals(BusinessTypeEnum.TEST_WORK_ORDER.getTypeCode()) ? ProductionStateEnum.TEST_PRODUCTION.getCode() : ProductionStateEnum.MASS_PRODUCTION.getCode())
                                    .skuId(o.getSkuId())
                                    .build();
                            bomEntity = bomService.getNewBom(bomTemplateMaterialSelectDTO);
                        }
                    }
                    return OrderMaterialListVO.builder()
                            .sourceOrderMaterialId(o.getWorkOrderId())
                            .businessUnitCode(o.getBusinessUnitCode())
                            .businessUnitName(o.getBusinessUnitName())
                            .sourceOrderNumber(o.getWorkOrderNumber())
                            .materialCode(o.getMaterialCode())
                            .materialName(codeMaterialMap.getOrDefault(o.getMaterialCode(), new MaterialEntity()).getName())
                            .skuId(o.getSkuId())
                            .bomVO(Objects.nonNull(bomEntity) ? BomVO.builder()
                                    .id(bomEntity.getId())
                                    .bomNum(bomEntity.getBomNum())
                                    .version(bomEntity.getVersion())
                                    .build() : null)
                            .planQuantity(o.getPlanQuantity())
                            // 下推数量 = 关联的下推记录的下推数量之和
                            .pushDownQuantity(pushDownQuantitySum)
                            // 下推次数 = 关联的下推记录个数总和
                            .pushDownTimes(pushDownTimes)
                            // 本次下推数量 = 计划数量 - 已下推数量
                            .thisPushDownQuantity(MathUtil.sub(o.getPlanQuantity(), pushDownQuantitySum) < 0 ? 0 : MathUtil.sub(o.getPlanQuantity(), pushDownQuantitySum))
                            // 下推状态
                            // 未下推(下推数量=0)、部分下推（0<下推数量<计划数量）、已下推（下推数量>=计划数量）
                            .pushDownStatusName(pushDownStatusName)
                            // 前端勾选的数据(按单查询时全勾选)
                            .isChoose(CollectionUtils.isEmpty(dto.getOrderMaterialIds()) || materialPushDown)
                            // 业务类型
                            .businessType(o.getBusinessType())
                            // 单据类型
                            .orderType(o.getOrderType())
                            .orderTypeName(orderTypeMap.get(o.getOrderType()))
                            // 工艺工序id
                            .craftProcedureId(craftProcedureIdMap.get(o.getWorkOrderNumber()))
                            .build();
                }
        ).collect(Collectors.toList());
        return vos;
    }

    @Override
    public List<CraftRoutePushWorkOrderVO.TargetOrderPushMaterialListVO> getTargetPushMaterialList(AbstractPushDTO pushDown) {
        List<CraftRoutePushWorkOrderVO.TargetOrderPushMaterialListVO> retList = new ArrayList<>();
        // 获取下推规则详情
        ActualPushDownConfigDTO pushDownConfig = pushDownConfigService.getPushDownConfig(pushDown.getItemId());
        // 获取物料行id
        List<SourceOrderPushDownMaterialListVO> sourcePushDownMaterialList = pushDown.getSourcePushDownMaterialList();

        // 普通下推、按日计划下推：
        // 1、根据下推配置来判断 是按工作中心还是工序下推
        //
        // 按工序日计划下推：
        // 1、工艺路线中仅有单个模型(制造单元/设备/班组)，无论什么情况都是按工序下推
        // 2、工艺路线中有混合模型，则制造单元按工作中心下推，设备/班组按工序下推
        List<WorkOrderProcedureDTO> workOrderProcedures = new ArrayList<>();
        boolean isProcedureDailyPlanPush = Objects.nonNull(sourcePushDownMaterialList.get(0).getCraftProcedureId());
        if (CraftSplitTypeEnum.PROCEDURE.getTypeCode().equals(pushDownConfig.getCraftSplitType()) || isProcedureDailyPlanPush) {
            // 按工序拆分
            for (SourceOrderPushDownMaterialListVO materialListVO : sourcePushDownMaterialList) {
                DefaultCraftPushDownDTO craftPushDownDTO = JacksonUtil.convertObject(materialListVO, DefaultCraftPushDownDTO.class);
                craftPushDownDTO.setFullPathCode(pushDownConfig.getFullPathCode());
                craftPushDownDTO.setSourceOrderMaterialCode(materialListVO.getMaterialCode());
                craftPushDownDTO.setSourceOrderPushDownQuantity(materialListVO.getThisPushDownQuantity());
                craftPushDownDTO.setDealId(materialListVO.getId());
                WorkOrderProcedureDTO workOrderProcedure = getWorkOrderProcedureByProcedure(craftPushDownDTO);
                workOrderProcedures.add(workOrderProcedure);
            }
        } else if (CraftSplitTypeEnum.WORK_CENTER.getTypeCode().equals(pushDownConfig.getCraftSplitType())) {
            // 按工作中心拆分
            for (SourceOrderPushDownMaterialListVO materialListVO : sourcePushDownMaterialList) {
                DefaultCraftPushDownDTO craftPushDownDTO = JacksonUtil.convertObject(materialListVO, DefaultCraftPushDownDTO.class);
                craftPushDownDTO.setFullPathCode(pushDownConfig.getFullPathCode());
                craftPushDownDTO.setSourceOrderMaterialCode(materialListVO.getMaterialCode());
                craftPushDownDTO.setSourceOrderPushDownQuantity(materialListVO.getThisPushDownQuantity());
                craftPushDownDTO.setDealId(materialListVO.getId());
                WorkOrderProcedureDTO workOrderProcedure = getWorkOrderProcedureByWorkCenterNewMethod(craftPushDownDTO);
                workOrderProcedures.add(workOrderProcedure);
            }
        }
        // 如果createWorkOrderCraftProcedures字段为空，则为无工艺下推，下推的工单无工作中心、工序、生产基本单元
        List<WorkOrderProcedureDTO> noCraftProcedureList = workOrderProcedures.stream().filter(o -> CollectionUtils.isEmpty(o.getCreateWorkOrderCraftProcedures())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noCraftProcedureList)) {
            noCraftCreateWorkOrder(noCraftProcedureList, pushDownConfig, retList);
        }

        // 以下是生成有工艺的工单
        List<WorkOrderProcedureDTO> hasCraftProcedureList = workOrderProcedures.stream().filter(o -> CollectionUtils.isNotEmpty(o.getCreateWorkOrderCraftProcedures())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(hasCraftProcedureList)) {
            craftCreateWorkOrder(hasCraftProcedureList, workOrderProcedures, pushDownConfig, retList);
        }
        // 是否过滤数量为0的生产工单
        if (Objects.nonNull(pushDownConfig.getIsFilterWorkOrder()) && pushDownConfig.getIsFilterWorkOrder()) {
            retList = retList.stream().filter(o -> o.getPlanQuantity() > 0).collect(Collectors.toList());
        }
        return retList;
    }

    /**
     * 无工艺下推
     */
    private void noCraftCreateWorkOrder(List<WorkOrderProcedureDTO> noCraftProcedureList, ActualPushDownConfigDTO pushDownConfig, List<CraftRoutePushWorkOrderVO.TargetOrderPushMaterialListVO> retList) {
        // 获取物料列表
        List<String> materialCodes = noCraftProcedureList.stream().map(WorkOrderProcedureDTO::getMaterialCode).collect(Collectors.toList());
        QueryWrapper<MaterialEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(MaterialEntity::getCode, materialCodes);
        Map<String, MaterialEntity> materialEntityMap = materialService.list(wrapper)
                .stream().collect(Collectors.toMap(MaterialEntity::getCode, v -> v));
        List<FieldMappingEntity> fieldMappingEntities = fieldMappingService.getFieldMappingList(FieldMappingSelectDTO.builder()
                .upstreamOrderType(Constant.PRODUCT_ORDER)
                .downstreamOrderType(Constant.WORK_ORDER)
                .build());
        Date now = new Date();

        // 如果派工状态为空，获取派工的业务配置
        FullPathCodeDTO dto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.WORK_ORDER_ASSIGNMENT_CONFIG).build();
        WorkOrderAssignmentConfigDTO config = businessConfigService.getValueDto(dto, WorkOrderAssignmentConfigDTO.class);
        Map<String, ProductOrderEntity> productOrderMap = new HashMap<>();
        // 根据生产订单的业务类型获取工单可选的业务类型以及单据类型
        List<BusinessTypeListVO> enableBusinessTypeListVOS = orderTypeConfigService.getOrderTypeListByOrderCategory(OrderTypeSelectDTO.builder()
                .showType(OrderTypeShowTypeEnum.SHOW_ENABLE.getCode())
                .categoryCode(CategoryTypeEnum.WORK_ORDER.getTypeCode())
                .build());
        // 判断生产订单是不是试产订单的业务类型下的单据类型，如果是则默认拿试产工单的业务类型下的单据类型
        List<BusinessTypeListVO> vos = orderTypeConfigService.getOrderTypeListByBusinessType(OrderTypeSelectDTO.builder()
                .businessCode(BusinessTypeEnum.TEST_PRODUCT_ORDER.getTypeCode())
                .showType(OrderTypeShowTypeEnum.SHOW_ALL.getCode())
                .build());
        List<String> businessCodes = vos.stream().map(BusinessTypeListVO::getBusinessTypeCode).collect(Collectors.toList());
        OrderTypeInfoVO defaultOrderType = null;
        for (WorkOrderProcedureDTO workOrderProcedure : noCraftProcedureList) {
            Date startDate = null;
            Date endDate = null;
            // 获取关联的生产订单
            ProductOrderEntity productOrderEntity = extProductOrderInterface.selectProductOrderByNumber(ProductOrderDetailDTO.builder().productOrderNumber(workOrderProcedure.getSourceOrderNumber()).build());
            ProductOrderMaterialEntity productOrderMaterialEntity = productOrderEntity.getProductOrderMaterial();
            // 根据下推配置怕判断是否需要自动设置计划时间
            if (pushDownConfig.getEnableAutoFillPlanTime()) {
                // 优先取timeDTO里的，说明有明确传递的时间，没有则默认取计划生产开始时间和计划生产结束时间
                startDate = Objects.nonNull(workOrderProcedure.getTimeDTO()) ? workOrderProcedure.getTimeDTO().getStartDate() : null;
                endDate = Objects.nonNull(workOrderProcedure.getTimeDTO()) ? workOrderProcedure.getTimeDTO().getEndDate() : null;
                if (Objects.isNull(startDate)) {
                    startDate = productOrderMaterialEntity.getPlanProductStartTime() == null ? now : productOrderMaterialEntity.getPlanProductStartTime();
                    endDate = productOrderMaterialEntity.getPlanProductEndTime() == null ? DateUtil.addSec(now, 1) : productOrderMaterialEntity.getPlanProductEndTime();
                }
            }
            // 根据下推配置判断是否需要过滤
            MaterialEntity materialEntity = materialEntityMap.get(workOrderProcedure.getMaterialCode());
            if (pushDownConfigService.isNeedFilter(pushDownConfig, materialEntity)) {
                continue;
            }
            if (businessCodes.contains(productOrderEntity.getType())) {
                defaultOrderType = orderTypeConfigService.getDefaultOrderTypeCodeByBusinessCode(BusinessTypeEnum.TEST_WORK_ORDER.getTypeCode());
                defaultOrderType.setIsEdit(false);
                enableBusinessTypeListVOS = enableBusinessTypeListVOS.stream()
                        .filter(vo -> vo.getBusinessTypeCode().equals(BusinessTypeEnum.TEST_WORK_ORDER.getTypeCode()))
                        .collect(Collectors.toList());
            } else {
                // 如果不是试产订单，则默认拿默认业务类型下的默认单据类型
                defaultOrderType = orderTypeConfigService.getDefaultOrderTypeCodeByCategoryCode(CategoryTypeEnum.WORK_ORDER.getTypeCode());
                defaultOrderType.setIsEdit(true);
                enableBusinessTypeListVOS = enableBusinessTypeListVOS.stream()
                        .filter(vo -> !vo.getBusinessTypeCode().equals(BusinessTypeEnum.TEST_WORK_ORDER.getTypeCode()))
                        .collect(Collectors.toList());
            }
            // 获取物料的特征参数
            materialEntity.setAuxiliaryAttrEntities(materialAuxiliaryAttrService.lambdaQuery().eq(MaterialAuxiliaryAttrEntity::getMaterialCode, materialEntity.getCode()).list());
            materialEntity.setSkuEntity(skuService.getById(productOrderMaterialEntity.getSkuId()));
            // 工作中心列表，如果是无工艺下推，则获取所有的工作中心列表
            List<WorkCenterEntity> workCenterEntities = workCenterService.list();
            // 下推次数
            int pushTimes = getProductOrderPushTimes(productOrderMaterialEntity.getProductOrderId());
            try {
                // 组转对象
                retList.add(
                        CraftRoutePushWorkOrderVO.TargetOrderPushMaterialListVO.builder()
                                .sourceOrderMaterialId(workOrderProcedure.getSourceOrderMaterialId())
                                .sourceOrderNumber(workOrderProcedure.getSourceOrderNumber())
                                .sourceOrderMaterialCode(workOrderProcedure.getSourceOrderMaterialCode())
                                .sourceOrderType(pushDownConfig.getSourceOrderType())
                                .targetOrderType(pushDownConfig.getTargetOrderType())
                                .productOrderNumber(workOrderProcedure.getSourceOrderNumber())
                                .saleOrderNumber(productOrderMaterialEntity.getSaleOrderCode())
                                .materialCode(workOrderProcedure.getMaterialCode())
                                .materialName(materialEntity.getName())
                                .materialFields(JacksonUtil.convertObject(materialEntity, com.yelink.dfscommon.entity.dfs.MaterialEntity.class))
                                .sourceOrderPushDownQuantity(workOrderProcedure.getSourceOrderPushDownQuantity())
                                // 工单的计划数量 = 下推数量 * 换算系数（使用场景：电池组装）
                                .planQuantity(workOrderProcedure.getSourceOrderPushDownQuantity())
                                .workCenterEntities(workCenterEntities)
                                .customerCode(productOrderEntity.getCustomerCode())
                                .customerName(productOrderEntity.getCustomerName())
                                .customerMaterialCode(productOrderMaterialEntity.getCustomerMaterialCode())
                                .customerMaterialName(productOrderMaterialEntity.getCustomerMaterialName())
                                .customerSpecification(productOrderMaterialEntity.getCustomerSpecification())
                                .skuId(productOrderMaterialEntity.getSkuId())
                                .assignmentState(Integer.valueOf(pushDownConfig.getTargetOrderState()).equals(WorkOrderStateEnum.RELEASED.getCode()) ? config.getAssignmentState() : AssignmentStateEnum.TO_BE_ASSIGNED.getType())
                                .plannedBatches(!materialEntity.getIsBatchMag() ? 0 : productOrderMaterialEntity.getPlannedBatches())
                                .actualBatches(productOrderMaterialEntity.getActualBatches())
                                .plansPerBatch(Boolean.FALSE.equals(materialEntity.getIsBatchMag()) ? 0 : productOrderMaterialEntity.getPlansPerBatch())
                                .startDate(startDate)
                                .endDate(endDate)
                                .pushTimes(pushTimes)
                                .orderType(defaultOrderType.getOrderType())
                                .businessType(defaultOrderType.getBusinessTypeCode())
                                .businessTypeListVOS(enableBusinessTypeListVOS)
                                .packageSchemeCode(productOrderMaterialEntity.getPackageSchemeCode())
                                .workOrderExtendFieldOne(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderExtendFieldOne", productOrderEntity, ProductOrderEntity.class))
                                .workOrderExtendFieldTwo(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderExtendFieldTwo", productOrderEntity, ProductOrderEntity.class))
                                .workOrderExtendFieldThree(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderExtendFieldThree", productOrderEntity, ProductOrderEntity.class))
                                .workOrderExtendFieldFour(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderExtendFieldFour", productOrderEntity, ProductOrderEntity.class))
                                .workOrderExtendFieldFive(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderExtendFieldFive", productOrderEntity, ProductOrderEntity.class))
                                .workOrderExtendFieldSix(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderExtendFieldSix", productOrderEntity, ProductOrderEntity.class))
                                .workOrderExtendFieldSeven(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderExtendFieldSeven", productOrderEntity, ProductOrderEntity.class))
                                .workOrderExtendFieldEight(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderExtendFieldEight", productOrderEntity, ProductOrderEntity.class))
                                .workOrderExtendFieldNine(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderExtendFieldNine", productOrderEntity, ProductOrderEntity.class))
                                .workOrderExtendFieldTen(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderExtendFieldTen", productOrderEntity, ProductOrderEntity.class))
                                .workOrderMaterialExtendFieldOne(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderMaterialExtendFieldOne", productOrderMaterialEntity, ProductOrderMaterialEntity.class))
                                .workOrderMaterialExtendFieldTwo(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderMaterialExtendFieldTwo", productOrderMaterialEntity, ProductOrderMaterialEntity.class))
                                .workOrderMaterialExtendFieldThree(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderMaterialExtendFieldThree", productOrderMaterialEntity, ProductOrderMaterialEntity.class))
                                .workOrderMaterialExtendFieldFour(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderMaterialExtendFieldFour", productOrderMaterialEntity, ProductOrderMaterialEntity.class))
                                .workOrderMaterialExtendFieldFive(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderMaterialExtendFieldFive", productOrderMaterialEntity, ProductOrderMaterialEntity.class))
                                .workOrderMaterialExtendFieldSix(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderMaterialExtendFieldSix", productOrderMaterialEntity, ProductOrderMaterialEntity.class))
                                .workOrderMaterialExtendFieldSeven(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderMaterialExtendFieldSeven", productOrderMaterialEntity, ProductOrderMaterialEntity.class))
                                .workOrderMaterialExtendFieldEight(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderMaterialExtendFieldEight", productOrderMaterialEntity, ProductOrderMaterialEntity.class))
                                .workOrderMaterialExtendFieldNine(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderMaterialExtendFieldNine", productOrderMaterialEntity, ProductOrderMaterialEntity.class))
                                .workOrderMaterialExtendFieldTen(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderMaterialExtendFieldTen", productOrderMaterialEntity, ProductOrderMaterialEntity.class))
                                .businessUnitCode(workOrderProcedure.getBusinessUnitCode())
                                .businessUnitName(workOrderProcedure.getBusinessUnitName())
                                .dealId(workOrderProcedure.getDealId())
                                .build()
                );
            } catch (Exception e) {
                log.error("字段映射异常");
                throw new ResponseException("字段映射异常");
            }
        }
    }

    /**
     * 生成存在工艺的工单
     */
    private void craftCreateWorkOrder(List<WorkOrderProcedureDTO> workOrderCraftProcedureDTOS, List<WorkOrderProcedureDTO> workOrderProcedures, ActualPushDownConfigDTO pushDownConfig, List<CraftRoutePushWorkOrderVO.TargetOrderPushMaterialListVO> retList) {
        // 遍历时一把查询工艺，防止频繁和数据库交互导致接口超时
        List<Integer> craftIds = workOrderCraftProcedureDTOS.stream().flatMap(o -> o.getCreateWorkOrderCraftProcedures().stream()).map(CraftProcedureEntity::getCraftId).collect(Collectors.toList());
        List<CraftEntity> crafts = JacksonUtil.convertArray(craftService.listByIds(craftIds), CraftEntity.class);
        Map<Integer, CraftEntity> craftMap = crafts.stream().collect(Collectors.toMap(CraftEntity::getCraftId, o -> o));
        // 获取工艺关联的工序（需过滤完全委外工序）
        List<CraftProcedureEntity> craftProcedureEntities = craftProcedureService.lambdaQuery()
                .in(CraftProcedureEntity::getCraftId, craftIds)
                .ne(CraftProcedureEntity::getIsSubContractingOperation, OutsourcingProcedureEnum.YES.getCode())
                .list();
        // 获取工序物料信息
        Map<String, ProcedureMaterialEntity> procedureMaterialMap = procedureMaterialService.lambdaQuery()
                .in(ProcedureMaterialEntity::getCraftId, craftIds)
                .list().stream()
                .collect(Collectors.toMap(o -> o.getCraftId() + Constants.UNDER_LINE + o.getProcedureId(), o -> o, (v1, v2) -> v1));

        Map<Integer, List<CraftProcedureEntity>> craftProcedureMapByCraftId = craftProcedureEntities.stream().collect(Collectors.groupingBy(CraftProcedureEntity::getCraftId));
        // 根据配置获取工序绑定的物料
        setCraftProcedureMaterialCode(workOrderProcedures, craftMap, procedureMaterialMap, pushDownConfig);

        // 获取物料列表
        List<String> materialCodes = workOrderProcedures.stream().flatMap(o -> o.getCreateWorkOrderCraftProcedures().stream()).map(CraftProcedureEntity::getMaterialCode).collect(Collectors.toList());
        QueryWrapper<MaterialEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(MaterialEntity::getCode, materialCodes);
        Map<String, MaterialEntity> materialEntityMap = materialService.list(wrapper)
                .stream().collect(Collectors.toMap(MaterialEntity::getCode, v -> v));

        // 组转成工艺工序数据
        groupConversionWorkOrderProcedureList(workOrderProcedures, craftProcedureMapByCraftId, materialEntityMap, pushDownConfig, retList, craftMap);
    }

    @Transactional(rollbackFor = Exception.class)
    @Async
    @Override
    public void pushWorkOrder(CraftRoutePushWorkOrderVO pushDown, String username, String code) {
        List<SourceOrderPushDownMaterialListVO> sourcePushDownMaterialList = pushDown.getSourcePushDownMaterialList();
        List<CraftRoutePushWorkOrderVO.TargetOrderPushMaterialListVO> pushOrderMaterialList = pushDown.getPushOrderMaterialList();
        ActualPushDownConfigDTO pushDownConfig = null;
        // 下推的单据
        List<String> pushSourceOrderNumbers = sourcePushDownMaterialList.stream().map(SourceOrderPushDownMaterialListVO::getSourceOrderNumber).distinct().collect(Collectors.toList());
        String progressKey = RedisKeyPrefix.PUSH_DOWN_PROGRESS + code;
        // 初始化进度，获取锁（给单加锁,同一个单，在没有下推完成之前，不能多次下推）
        // 对用户也要加锁（如果不加，后续取缓存的时候有问题）
        commonService.initLockProgress(progressKey, pushSourceOrderNumbers, RedisKeyPrefix.PRODUCT_ORDER_PUSH_DOWN_LOCK, 30, TimeUnit.MINUTES, username);
        try {
            WorkOrderService workOrderService = SpringUtil.getBean(WorkOrderService.class);
            // 获取下推规则配置项
            pushDownConfig = pushDownConfigService.getPushDownConfig(pushDown.getItemId());
            // 获取下推规则详情
            OrderPushDownRuleItemVO pushDownInfo = pushDownConfigService.getPushDownInfo(pushDown.getItemId());
            // 生产工单是否开启审批
            boolean enableApproval = approveConfigService.getConfigByCode(ApproveModuleEnum.WORK_ORDER.getCode());
            // 下推成生效状态,但开启了审批
            boolean approvalAndReleased = enableApproval && Integer.valueOf(pushDownConfig.getTargetOrderState()).equals(WorkOrderStateEnum.RELEASED.getCode());
            Integer pushWorkOrderState = approvalAndReleased ? WorkOrderStateEnum.CREATED.getCode() : Integer.parseInt(pushDownConfig.getTargetOrderState());
            // 获取新增的生产订单号(如果是合并下推，则获取新增的合并单号)
            List<ProductOrderEntity> productOrderEntities = extProductOrderInterface.getListByProductOrderNumbers(pushSourceOrderNumbers);
            List<ProductOrderMaterialEntity> productOrderMaterialEntities = productOrderEntities.stream().map(ProductOrderEntity::getProductOrderMaterial).collect(Collectors.toList());
            // 被合的生产订单，不能进行下推
            long count = productOrderMaterialEntities.stream().filter(o -> o.getMergedState().equals(OrderMergeStateEnum.IS_MERGED.getCode())).count();
            if (count > 0) {
                throw new ResponseException("合单操作后的生产订单不能进行下推");
            }
            List<OrderPushDownRecordEntity> recordEntities = new ArrayList<>();
            Map<String, ProductOrderMaterialEntity> productOrderMaterialMap = productOrderMaterialEntities.stream().collect(Collectors.toMap(ProductOrderMaterialEntity::getProductOrderNumber, v -> v));
            List<WorkOrderEntity> workOrderEntities = new ArrayList<>();
            // 生成工单
            int i = 1;
            for (CraftRoutePushWorkOrderVO.TargetOrderPushMaterialListVO targetOrderPushMaterialListVO : pushOrderMaterialList) {
                // 生成创建态工单
                WorkOrderEntity workOrderEntity = pushDownToWorkOrder(pushDown.getRuleId(), targetOrderPushMaterialListVO, username, productOrderMaterialMap, workOrderService, pushDownConfig.getCarryFile());
                workOrderEntities.add(workOrderEntity);
                // 需要创建的工单是生效状态
                if (!approvalAndReleased && pushWorkOrderState.equals(WorkOrderStateEnum.RELEASED.getCode())) {
                    workOrderEntity.setState(WorkOrderStateEnum.RELEASED.getCode());
                    workOrderService.updateByWorkId(WorkOrderSmartUpdateDTO.onlyState(workOrderEntity, username));
                }
                // 记录下推日志
                recordEntities.add(OrderPushDownRecordEntity.builder()
                        .sourceOrderType(targetOrderPushMaterialListVO.getSourceOrderType())
                        .sourceOrderNumber(targetOrderPushMaterialListVO.getSourceOrderNumber())
                        .sourceOrderMaterialId(targetOrderPushMaterialListVO.getSourceOrderMaterialId())
                        .materialCode(targetOrderPushMaterialListVO.getSourceOrderMaterialCode())
                        .pushDownQuantity(targetOrderPushMaterialListVO.getSourceOrderPushDownQuantity())
                        .targetOrderType(targetOrderPushMaterialListVO.getTargetOrderType())
                        .targetOrderNumber(workOrderEntity.getWorkOrderNumber())
                        .targetOrderPushDownQuantity(workOrderEntity.getPlanQuantity())
                        .targetOrderMaterialCode(workOrderEntity.getMaterialCode())
                        .targetOrderMaterialId(workOrderEntity.getWorkOrderId())
                        .pushDownCode(code)
                        .createBy(username)
                        .dealId(targetOrderPushMaterialListVO.getDealId())
                        .build());
                // 手动更新进度
                Double processPercent = MathUtil.divideDouble(i, pushOrderMaterialList.size(), 2);
                commonService.updateProgress(progressKey, processPercent, String.format("正在处理中，请耐心等候...当前下推了%s个生产工单；", i));
                i = i + 1;
            }
            // 记录下推记录
            orderPushDownRecordService.batchAddRecord(recordEntities);
            // 如果按日计划下推，需要调用ams接口记录下推日志
            if (pushDownInfo.getInstanceType().equals(PushDownInstanceTypeEnum.PRODUCT_ORDER_DAILY_PUSH)) {
                List<ProductOrderPlanPushRecordAddDTO> orderPlanPushRecordAddDTOS = recordEntities.stream().map(record ->
                        ProductOrderPlanPushRecordAddDTO.builder()
                                .dailyPlanId(record.getDealId())
                                .relateOrder(record.getSourceOrderNumber())
                                .pushAmount(record.getTargetOrderPushDownQuantity())
                                .createBy(record.getCreateBy())
                                .createTime(record.getRecordDate())
                                .build()
                ).collect(Collectors.toList());
                productOrderDailyPlanInterface.addDailyPlanPushRecord(orderPlanPushRecordAddDTOS);
            }
            // 手动更新进度
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(String.format("下推了%s个生产工单，其中成功%s个，失败0个；", pushOrderMaterialList.size(), pushOrderMaterialList.size()));
            if (approvalAndReleased) {
                stringBuilder.append("\n当前单据已开启审批流程现已下推单据为创建态，请审批后进行生效。");
            }
            commonService.updateProgress(progressKey, 1.0, stringBuilder.toString());

            // 事务回调方法，事务成功提交之后执行，否则有几率回填不成功
            if (TransactionSynchronizationManager.isActualTransactionActive()) {
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        for (WorkOrderEntity workOrderEntity : workOrderEntities) {
                            WorkOrderServiceImpl bean = SpringUtil.getBean(WorkOrderServiceImpl.class);
                            bean.dealAfterAdd(workOrderEntity);
                        }
                    }
                });
            }

        } catch (Exception e) {
            commonService.importProgressException(progressKey, e);
            log.warn("下推过程中出错", e);
        } finally {
            // 删除锁
            commonService.releaseLock(pushSourceOrderNumbers, RedisKeyPrefix.PRODUCT_ORDER_PUSH_DOWN_LOCK, username);
        }
    }

    @Override
    @Async
    public void batchDelete(WorkOrderNumbersDTO workOrderNumbersDTO, String code, OperationLogEntity operationLog) {
        String username = operationLog.getUsername();
        List<String> workOrderNumbers = workOrderNumbersDTO.getWorkOrderNumbers();
        if (CollectionUtils.isEmpty(workOrderNumbers)) {
            return;
        }
        WorkOrderService workOrderService = SpringUtil.getBean(WorkOrderService.class);
        // 删除的单据
        String progressKey = RedisKeyPrefix.DELETE_PROGRESS + code;
        // 初始化进度，获取锁（给单加锁,同一个单，在没有删除完成之前，不能多次删除）
        // 对用户也要加锁（如果不加，后续取缓存的时候有问题）
        commonService.initLockProgress(progressKey, workOrderNumbers, RedisKeyPrefix.DELETE_WORK_ORDER_LOCK, 30, TimeUnit.MINUTES, username);
        try {
            int i = 1;
            List<Integer> workOrderIds = workOrderService.lambdaQuery()
                    .select(WorkOrderEntity::getWorkOrderId)
                    .in(WorkOrderEntity::getWorkOrderNumber, workOrderNumbers)
                    .list().stream()
                    .map(WorkOrderEntity::getWorkOrderId)
                    .collect(Collectors.toList());
            for (Integer workOrderId : workOrderIds) {
                WorkOrderEntity delWorkOrder = workOrderService.deleteById(workOrderId, username);
                // 手动更新进度
                Double processPercent = MathUtil.divideDouble(i, workOrderNumbers.size(), 2);
                commonService.updateProgress(progressKey, processPercent, String.format("正在处理中，请耐心等候...当前删除了%s个生产工单；", i));
                i = i + 1;
                operationLog.setModule("工单管理");
                operationLog.setType(OperationType.DELETE);
                operationLog.setDes("工单:" + delWorkOrder.getWorkOrderNumber() + "被批量删除");
                operationLog.setId(null);
                operationLogService.manualInsert(operationLog);
            }
            // 手动更新进度
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(String.format("删除了%s个生产工单，其中成功%s个，失败0个；", workOrderNumbers.size(), workOrderNumbers.size()));
            commonService.updateProgress(progressKey, 1.0, stringBuilder.toString());
        } catch (Exception e) {
            commonService.importProgressException(progressKey, e);
            log.warn("删除工单过程中出错", e);
        } finally {
            // 删除锁
            commonService.releaseLock(workOrderNumbers, RedisKeyPrefix.DELETE_WORK_ORDER_LOCK, username);
        }
    }

    @Override
    public List<WorkOrderCraftVO> getBindCraftInfo(WorkOrderNumbersDTO workOrderNumbersDTO) {
        WorkOrderService workOrderService = SpringUtil.getBean(WorkOrderService.class);
        List<WorkOrderEntity> workOrderEntities = workOrderService.lambdaQuery().in(WorkOrderEntity::getWorkOrderNumber, workOrderNumbersDTO.getWorkOrderNumbers()).list();
        List<Integer> craftIds = workOrderEntities.stream().map(WorkOrderEntity::getCraftId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Integer, com.yelink.dfs.entity.product.CraftEntity> craftMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(craftIds)) {
            List<com.yelink.dfs.entity.product.CraftEntity> craftEntities = craftService.listByIds(craftIds);
            craftMap = craftEntities.stream().collect(Collectors.toMap(com.yelink.dfs.entity.product.CraftEntity::getCraftId, o -> o));
        }
        List<MaterialCodeAndSkuIdSelectDTO> materialCodes = workOrderEntities.stream()
                .map(entity -> MaterialCodeAndSkuIdSelectDTO.builder()
                        .materialCode(entity.getMaterialCode())
                        .skuId(entity.getSkuId()).build())
                .collect(Collectors.toList());
        Map<String, MaterialEntity> mainMaterialMap = materialService.getMaterialEntity(materialCodes);

        Map<Integer, com.yelink.dfs.entity.product.CraftEntity> finalCraftMap = craftMap;
        List<WorkOrderCraftVO> workOrderCraftVOS = workOrderEntities.stream().map(workOrderEntity -> {
            com.yelink.dfs.entity.product.CraftEntity craftEntity = finalCraftMap.get(workOrderEntity.getCraftId());
            MaterialEntity materialEntity = mainMaterialMap.get(ColumnUtil.getMaterialSku(workOrderEntity.getMaterialCode(), workOrderEntity.getSkuId()));
            return WorkOrderCraftVO.builder()
                    .orderId(workOrderEntity.getWorkOrderId())
                    .orderNumber(workOrderEntity.getWorkOrderNumber())
                    .isTemplate(Objects.nonNull(craftEntity) ? craftEntity.getIsTemplate() : null)
                    .craftId(Objects.nonNull(craftEntity) ? craftEntity.getCraftId() : null)
                    .craftCode(Objects.nonNull(craftEntity) ? craftEntity.getCraftCode() : null)
                    .craftName(Objects.nonNull(craftEntity) ? craftEntity.getName() : null)
                    .version(Objects.nonNull(craftEntity) ? craftEntity.getCraftVersion() : null)
                    .materialFields(JacksonUtil.convertObject(materialEntity, MaterialFieldVO.class))
                    .build();
        }).collect(Collectors.toList());
        // 过滤工艺配置项
        if (StringUtils.isNotBlank(workOrderNumbersDTO.getCraftConditionFilter())) {
            if (workOrderNumbersDTO.getCraftConditionFilter().equals(CraftRelatedConditionEnum.CRAFT.getCode())) {
                workOrderCraftVOS = workOrderCraftVOS.stream().filter(workOrderCraftVO -> Objects.nonNull(workOrderCraftVO.getIsTemplate()) && !workOrderCraftVO.getIsTemplate()).collect(Collectors.toList());
            } else if (workOrderNumbersDTO.getCraftConditionFilter().equals(CraftRelatedConditionEnum.CRAFT_TEMPLATE.getCode())) {
                workOrderCraftVOS = workOrderCraftVOS.stream().filter(workOrderCraftVO -> Objects.nonNull(workOrderCraftVO.getIsTemplate()) && workOrderCraftVO.getIsTemplate()).collect(Collectors.toList());
            } else {
                workOrderCraftVOS = workOrderCraftVOS.stream().filter(workOrderCraftVO -> Objects.isNull(workOrderCraftVO.getIsTemplate())).collect(Collectors.toList());
            }
        }
        return workOrderCraftVOS;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeWorkOrderInvestProductBasicUnit(WorkOrderProductBasicUnitChangeDTO changeDTO) {
        WorkOrderEntity workOrderEntity = this.lambdaQuery().eq(WorkOrderEntity::getWorkOrderNumber, changeDTO.getWorkOrderNumber()).one();
        if (Objects.isNull(workOrderEntity)) {
            return;
        }
        WorkOrderService workOrderService = SpringUtil.getBean(WorkOrderService.class);
        // 可以自定义投产时间
        Date refreshInvestTime = Objects.nonNull(changeDTO.getTime()) ? changeDTO.getTime() : new Date();
        String username = changeDTO.getUsername();
        WorkCenterEntity workCenterEntity = workCenterService.lambdaQuery().eq(WorkCenterEntity::getId, workOrderEntity.getWorkCenterId()).one();
        List<Integer> bindProductBasicUnitIds = changeDTO.getProductBasicUnitIds();
        if (CollectionUtils.isEmpty(bindProductBasicUnitIds)) {
            throw new ResponseException(RespCodeEnum.DFS_MUST_CHOSE_ONE_PRODUCT_BASIC_UNIT_WHEN_INVEST);
        }
        List<WorkOrderBasicUnitRelationEntity> basicUnitRelationEntities = basicUnitRelationService.lambdaQuery()
                .eq(WorkOrderBasicUnitRelationEntity::getWorkOrderNumber, changeDTO.getWorkOrderNumber())
                .list();
        // 如果绑定的生产基本单元不在工单可选的生产基本单元列表中，则将绑定的数据加入可选列表中。
        addNewProductBasicUnitRelation(changeDTO, basicUnitRelationEntities, workCenterEntity, refreshInvestTime);
        // 将选择的生产基本单元投产,未被选择设置为未投产，并刷新对应的投产记录。
        // （投产结束时间为空的情况）已经投产的无需新增投产记录，但是如果改为非投产，则刷新该记录下的结束时间。
        // 无投产记录或者存在投产结束时间，未投产改为已投产的则新增一条记录，记录开始时间。
        bindProductBasicUnit(changeDTO, workCenterEntity.getType(), workCenterEntity.getId(), refreshInvestTime, username);
        // 占用其他工单的生产基本单元，需要设置为未投产，并刷新对应的投产记录(当前只有设备才这么做，班组和制造单元需要通过软件参数，判断允不允许多张工单投产同个生产基本单元)
        if (workCenterEntity.getType().equals(WorkCenterTypeEnum.DEVICE.getCode())) {
            unbindProductBasic(changeDTO, workCenterEntity.getType(), refreshInvestTime);
        }
        if (workCenterEntity.getType().equals(WorkCenterTypeEnum.LINE.getCode()) || workCenterEntity.getType().equals(WorkCenterTypeEnum.TEAM.getCode())) {
            // 该配置为：同一产线/班组是否允许多张工单投产
            boolean enable = Boolean.parseBoolean(businessConfigValueService.getValue(ConfigConstant.WORK_ORDER_CONCURRENT));
            if (!enable) {
                boolean hangUpOther = Boolean.parseBoolean(businessConfigValueService.getValue(ConfigConstant.HANG_UP_OTHER_WORK_ORDER));
                if (hangUpOther) {
                    // 挂起所选生产基本单元关联的其他工单
                    unbindProductBasic(changeDTO, workCenterEntity.getType(), refreshInvestTime);
                } else {
                    // 不允许多张工单投产且不允许挂起其他工单，则直接报错
                    throw new ResponseException(RespCodeEnum.WORK_ORDER_EDIT_FALL);
                }
            }
        }

        // 以下代码为兼容历史逻辑
        double plannedWorkHours = workOrderService.calculatePlannedWorkHours(workOrderEntity);
        workOrderEntity.setPlannedWorkingHours(plannedWorkHours);
        workOrderService.lambdaUpdate().eq(WorkOrderEntity::getWorkOrderId, workOrderEntity.getWorkOrderId())
                .set(WorkOrderEntity::getPlannedWorkingHours, plannedWorkHours)
                .update();
        //更新制造单元工单关联关系
        workOrderEntity.setProductBasicUnits(basicUnitRelationService.lambdaQuery().eq(WorkOrderBasicUnitRelationEntity::getWorkOrderNumber, changeDTO.getWorkOrderNumber()).list());
        workOrderService.updateProductLineRelation(workOrderEntity);
        // 优先将投入使用的生产基本单元更新到工单表中的生产基本单元id
        if (workCenterEntity.getType().equals(WorkCenterTypeEnum.LINE.getCode())) {
            ProductionLineService lineService = SpringUtil.getBean(ProductionLineService.class);
            com.yelink.dfs.entity.manufacture.ProductionLineEntity lineEntity = lineService.getById(bindProductBasicUnitIds.get(0));
            workOrderService.lambdaUpdate().eq(WorkOrderEntity::getWorkOrderNumber, changeDTO.getWorkOrderNumber())
                    .set(WorkOrderEntity::getLineId, lineEntity.getProductionLineId())
                    .set(WorkOrderEntity::getLineCode, lineEntity.getProductionLineCode())
                    .set(WorkOrderEntity::getLineName, lineEntity.getName())
                    .update();
        } else if (workCenterEntity.getType().equals(WorkCenterTypeEnum.TEAM.getCode())) {
            workOrderService.lambdaUpdate().eq(WorkOrderEntity::getWorkOrderNumber, changeDTO.getWorkOrderNumber())
                    .set(WorkOrderEntity::getTeamId, bindProductBasicUnitIds.get(0)).update();
        } else {
            workOrderService.lambdaUpdate().eq(WorkOrderEntity::getWorkOrderNumber, changeDTO.getWorkOrderNumber())
                    .set(WorkOrderEntity::getDeviceId, bindProductBasicUnitIds.get(0)).update();
        }
    }

    /**
     * 增量同步工单关联的生产基本单元
     */
    private void addNewProductBasicUnitRelation(WorkOrderProductBasicUnitChangeDTO changeDTO, List<WorkOrderBasicUnitRelationEntity> basicUnitRelationEntities, WorkCenterEntity workCenterEntity, Date refreshInvestTime) {
        List<Integer> workOrderOptionalProductUnitIds = basicUnitRelationEntities.stream().map(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitId).collect(Collectors.toList());
        List<Integer> newProductUnitIds = changeDTO.getProductBasicUnitIds().stream()
                .filter(bindId -> !workOrderOptionalProductUnitIds.contains(bindId))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(newProductUnitIds)) {
            Map<Integer, String> nameMap = new HashMap<>();
            Map<Integer, String> codeMap = new HashMap<>();
            if (workCenterEntity.getType().equals(WorkCenterTypeEnum.TEAM.getCode())) {
                List<SysTeamEntity> sysTeamEntities = teamService.listByIds(newProductUnitIds);
                nameMap = sysTeamEntities.stream().collect(Collectors.toMap(SysTeamEntity::getId, SysTeamEntity::getTeamName));
                codeMap = sysTeamEntities.stream().collect(Collectors.toMap(SysTeamEntity::getId, SysTeamEntity::getTeamCode));
            } else if (workCenterEntity.getType().equals(WorkCenterTypeEnum.LINE.getCode())) {
                ProductionLineService lineService = SpringUtil.getBean(ProductionLineService.class);
                List<com.yelink.dfs.entity.manufacture.ProductionLineEntity> productionLineEntities = lineService.listByIds(newProductUnitIds);
                nameMap = productionLineEntities.stream().collect(Collectors.toMap(com.yelink.dfs.entity.manufacture.ProductionLineEntity::getProductionLineId, com.yelink.dfs.entity.manufacture.ProductionLineEntity::getName));
                codeMap = productionLineEntities.stream().collect(Collectors.toMap(com.yelink.dfs.entity.manufacture.ProductionLineEntity::getProductionLineId, com.yelink.dfs.entity.manufacture.ProductionLineEntity::getProductionLineCode));
            } else if (workCenterEntity.getType().equals(WorkCenterTypeEnum.DEVICE.getCode())) {
                List<DeviceEntity> deviceEntities = deviceService.listByIds(newProductUnitIds);
                nameMap = deviceEntities.stream().collect(Collectors.toMap(DeviceEntity::getDeviceId, DeviceEntity::getDeviceName));
                codeMap = deviceEntities.stream().collect(Collectors.toMap(DeviceEntity::getDeviceId, DeviceEntity::getDeviceCode));
            }
            Map<Integer, String> finalCodeMap = codeMap;
            Map<Integer, String> finalNameMap = nameMap;
            List<WorkOrderBasicUnitRelationEntity> newRelations = newProductUnitIds.stream().map(newProductUnitId -> WorkOrderBasicUnitRelationEntity.builder()
                            .workOrderNumber(changeDTO.getWorkOrderNumber())
                            .workCenterId(workCenterEntity.getId())
                            .workCenterType(workCenterEntity.getType())
                            .productionBasicUnitId(newProductUnitId)
                            .productionBasicUnitCode(finalCodeMap.get(newProductUnitId))
                            .productionBasicUnitName(finalNameMap.get(newProductUnitId))
                            .isolationId(workCenterEntity.getId() + Constants.CROSSBAR + newProductUnitId)
                            .createTime(refreshInvestTime)
                            .build())
                    .collect(Collectors.toList());
            basicUnitRelationService.saveBatch(newRelations);
        }
    }

    /**
     * 如果是设备投产，需要更新设备运行记录表
     */
    private void refreshDeviceResumeDaily(WorkOrderProductBasicUnitChangeDTO changeDTO, WorkCenterEntity workCenterEntity, List<Integer> bindProductBasicUnitIds, Date refreshInvestTime, WorkOrderEntity workOrderEntity, List<Integer> notIncludeInvestUnitIds) {
        if (workCenterEntity.getType().equals(WorkCenterTypeEnum.DEVICE.getCode())) {
            for (Integer bindProductBasicUnitId : bindProductBasicUnitIds) {
                // 找到最新的设备运行记录
                RecordDeviceResumeDailyEntity one = recordDeviceResumeDailyService.lambdaQuery()
                        .eq(RecordDeviceResumeDailyEntity::getWorkOrderNumber, changeDTO.getWorkOrderNumber())
                        .eq(RecordDeviceResumeDailyEntity::getDeviceId, bindProductBasicUnitId)
                        .orderByDesc(RecordDeviceResumeDailyEntity::getCreateTime)
                        .last("limit 1")
                        .one();
                if (Objects.nonNull(one)) {
                    continue;
                }
                recordDeviceResumeDailyService.save(RecordDeviceResumeDailyEntity.builder()
                        .deviceId(bindProductBasicUnitId)
                        .deviceState(DevicesStateEnum.RUNNING.getCode())
                        .recordDate(refreshInvestTime)
                        .recordTime(refreshInvestTime)
                        .workOrderNumber(changeDTO.getWorkOrderNumber())
                        .workOrderState(WorkOrderStateEnum.INVESTMENT.getCode())
                        .materialCode(workOrderEntity.getMaterialCode())
                        .startTime(refreshInvestTime)
                        .createTime(refreshInvestTime)
                        .updateTime(refreshInvestTime)
                        .build());
            }
            for (Integer notIncludeInvestUnitId : notIncludeInvestUnitIds) {
                // 找到最新的设备运行记录
                RecordDeviceResumeDailyEntity last = recordDeviceResumeDailyService.lambdaQuery()
                        .eq(RecordDeviceResumeDailyEntity::getWorkOrderNumber, changeDTO.getWorkOrderNumber())
                        .eq(RecordDeviceResumeDailyEntity::getDeviceId, notIncludeInvestUnitId)
                        .orderByDesc(RecordDeviceResumeDailyEntity::getCreateTime)
                        .last("limit 1")
                        .one();
                if (Objects.isNull(last)) {
                    continue;
                }
                // 没有尾部时间则取当前时间
                Date tempEndTime = last.getEndTime() != null ? last.getEndTime() : refreshInvestTime;
                long lastDelta = tempEndTime.getTime() - last.getStartTime().getTime();
                recordDeviceResumeDailyService.lambdaUpdate()
                        .eq(RecordDeviceResumeDailyEntity::getId, last.getId())
                        .set(RecordDeviceResumeDailyEntity::getEndTime, refreshInvestTime)
                        .set(RecordDeviceResumeDailyEntity::getUpdateTime, refreshInvestTime)
                        .set(RecordDeviceResumeDailyEntity::getDurationH, calDurationH(lastDelta))
                        .update();
            }
        }
    }

    private BigDecimal calDurationH(long delta) {
        BigDecimal divisor = BigDecimal.valueOf(1000 * 60 * 60);
        return BigDecimal.valueOf(delta).divide(divisor, 2, RoundingMode.HALF_UP);
    }

    @Override
    public Page<WorkOrderBasicUnitInputRecordEntity> getWorkOrderInputRecords(WorkOrderBasicUnitInvestSelectDTO selectDTO) {
        LambdaQueryWrapper<WorkOrderBasicUnitInputRecordEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(selectDTO.getWorkOrderNumber()), WorkOrderBasicUnitInputRecordEntity::getWorkOrderNumber, selectDTO.getWorkOrderNumber());
        wrapper.in(CollectionUtils.isNotEmpty(selectDTO.getProductBasicUnitIds()), WorkOrderBasicUnitInputRecordEntity::getProductionBasicUnitId, selectDTO.getProductBasicUnitIds());
        wrapper.orderByDesc(WorkOrderBasicUnitInputRecordEntity::getId);
        Page<WorkOrderBasicUnitInputRecordEntity> page = basicUnitInputRecordService.page(selectDTO.buildPage(), wrapper);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return page;
        }
        Map<String, String> nickNames = userService.getUserNameNickMap(null);
        for (WorkOrderBasicUnitInputRecordEntity record : page.getRecords()) {
            record.setUserNickName(nickNames.getOrDefault(record.getUsername(), record.getUsername()));
        }
        return page;
    }

    @Override
    public void dealProductBasicUnitRecordWhenHangupWorkOrder(String workOrderNumber) {
        // 如果工单挂起/完成/关闭/取消，则需要将工单目前正在投产的生产基本单元设置投产结束时间
        Date date = new Date();
        basicUnitRelationService.lambdaUpdate()
                .eq(WorkOrderBasicUnitRelationEntity::getWorkOrderNumber, workOrderNumber)
                .set(WorkOrderBasicUnitRelationEntity::getIsProducing, false)
                .update();
        List<Integer> recordIds = basicUnitInputRecordService.lambdaQuery()
                .eq(WorkOrderBasicUnitInputRecordEntity::getWorkOrderNumber, workOrderNumber)
                .isNull(WorkOrderBasicUnitInputRecordEntity::getEndTime)
                .list().stream().map(WorkOrderBasicUnitInputRecordEntity::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(recordIds)) {
            basicUnitInputRecordService.lambdaUpdate()
                    .in(WorkOrderBasicUnitInputRecordEntity::getId, recordIds)
                    .set(WorkOrderBasicUnitInputRecordEntity::getEndTime, date)
                    .update();
        }
    }

    @Override
    public List<WorkOrderBasicUnitRelationVO> getWorkOrderAllProductBasicUnit(WorkOrderNumberDTO workOrderNumberDTO) {
        List<WorkOrderBasicUnitRelationVO> list = new ArrayList<>();
        WorkOrderService workOrderService = SpringUtil.getBean(WorkOrderService.class);
        WorkOrderEntity workOrderEntity = workOrderService.lambdaQuery().eq(WorkOrderEntity::getWorkOrderNumber, workOrderNumberDTO.getWorkOrderNumber()).one();
        // 获取工单关联的工艺工序id
        String craftProcedureIds = workOrderProcedureRelationService.lambdaQuery().eq(WorkOrderProcedureRelationEntity::getWorkOrderNumber, workOrderNumberDTO.getWorkOrderNumber())
                .list().stream()
                .map(WorkOrderProcedureRelationEntity::getCraftProcedureId)
                .map(String::valueOf)
                .collect(Collectors.joining(Constants.SEP));
        if (workOrderEntity.getWorkCenterType().equals(WorkCenterTypeEnum.LINE.getCode())) {
            List<com.yelink.dfs.entity.manufacture.ProductionLineEntity> productionLineEntities = workCenterService.listLineByCenterId(workOrderEntity.getWorkCenterId(), null, null, craftProcedureIds);
            list = productionLineEntities.stream().map(o -> {
                // 查询该生产基本单元正在被占用的工单
                ModelEntity modelEntity = modelService.lambdaQuery().eq(ModelEntity::getId, o.getModelId()).one();
                String occupiedWorkOrderNumber = basicUnitRelationService.getOccupiedWorkOrderNumber(o.getWorkCenterId(), o.getProductionLineId());
                return WorkOrderBasicUnitRelationVO.builder()
                        .productionBasicUnitId(o.getProductionLineId())
                        .productionBasicUnitCode(o.getProductionLineCode())
                        .productionBasicUnitName(o.getName())
                        .productionBasicUnitTypeId(modelEntity.getId())
                        .productionBasicUnitTypeName(modelEntity.getName())
                        .occupiedWorkOrderNumber(occupiedWorkOrderNumber)
                        .build();
            }).collect(Collectors.toList());
        } else if (workOrderEntity.getWorkCenterType().equals(WorkCenterTypeEnum.DEVICE.getCode())) {
            List<DeviceEntity> deviceEntities = workCenterService.listDeviceByCenterId(workOrderEntity.getWorkCenterId(), null, null, craftProcedureIds);
            list = deviceEntities.stream().map(o -> {
                ModelEntity modelEntity = modelService.lambdaQuery().eq(ModelEntity::getId, o.getModelId()).one();
                // 查询该生产基本单元正在被占用的工单
                WorkCenterDeviceEntity one = workCenterDeviceService.lambdaQuery().select(WorkCenterDeviceEntity::getWorkCenterId).eq(WorkCenterDeviceEntity::getDeviceId, o.getDeviceId()).one();
                String occupiedWorkOrderNumber = Objects.nonNull(one) ? basicUnitRelationService.getOccupiedWorkOrderNumber(one.getWorkCenterId(), o.getDeviceId()) : null;
                return WorkOrderBasicUnitRelationVO.builder()
                        .productionBasicUnitId(o.getDeviceId())
                        .productionBasicUnitCode(o.getDeviceCode())
                        .productionBasicUnitName(o.getDeviceName())
                        .productionBasicUnitTypeId(modelEntity.getId())
                        .productionBasicUnitTypeName(modelEntity.getName())
                        .occupiedWorkOrderNumber(occupiedWorkOrderNumber)
                        .build();
            }).collect(Collectors.toList());
        } else {
            List<SysTeamEntity> sysTeamEntities = workCenterService.listTeamByCenterId(workOrderEntity.getWorkCenterId(), null, null);
            list = sysTeamEntities.stream().map(o -> {
                TeamTypeDefEntity teamTypeDefEntity = teamTypeDefService.lambdaQuery().eq(TeamTypeDefEntity::getId, o.getTeamType()).one();
                // 查询该生产基本单元正在被占用的工单
                WorkCenterTeamEntity one = workCenterTeamService.lambdaQuery().select(WorkCenterTeamEntity::getWorkCenterId).eq(WorkCenterTeamEntity::getTeamId, o.getId()).one();
                String occupiedWorkOrderNumber = Objects.nonNull(one) ? basicUnitRelationService.getOccupiedWorkOrderNumber(one.getWorkCenterId(), o.getId()) : null;
                return WorkOrderBasicUnitRelationVO.builder()
                        .productionBasicUnitId(o.getId())
                        .productionBasicUnitCode(o.getTeamCode())
                        .productionBasicUnitName(o.getTeamName())
                        .productionBasicUnitTypeId(teamTypeDefEntity.getId())
                        .productionBasicUnitTypeName(teamTypeDefEntity.getTypeDefName())
                        .occupiedWorkOrderNumber(occupiedWorkOrderNumber)
                        .build();
            }).collect(Collectors.toList());
        }
        return list;
    }

    @Override
    public List<CommonType> getBasicUnitType(ProductionBasicUnitTypeSelectDTO selectDTO) {
        List<CommonType> list = new ArrayList<>();
        List<Integer> lineModelIds = new ArrayList<>();
        List<Integer> deviceModelIds = new ArrayList<>();
        List<Integer> teamModelIds = new ArrayList<>();
        List<Integer> workCenterIds = selectDTO.getWorkCenterIds();
        List<Integer> aids = selectDTO.getAids();
        if (CollectionUtils.isNotEmpty(aids)) {
            WorkCenterSelectDTO dto = WorkCenterSelectDTO.builder().aids(aids).build();
            Page<WorkCenterEntity> page = workCenterService.list(dto);
            if (CollectionUtils.isEmpty(page.getRecords())) {
                return new ArrayList<>();
            }
            workCenterIds = page.getRecords().stream().map(WorkCenterEntity::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(selectDTO.getWorkCenterIds())) {
                workCenterIds = workCenterIds.stream().filter(o -> selectDTO.getWorkCenterIds().contains(o)).collect(Collectors.toList());
            }
        }
        if (CollectionUtils.isNotEmpty(workCenterIds)) {
            // 查询制造单元模型(主资源和关联资源都要查出)
            ProductionLineService productionLineService = SpringUtil.getBean(ProductionLineService.class);
            lineModelIds = productionLineService.lambdaQuery()
                    .in(com.yelink.dfs.entity.manufacture.ProductionLineEntity::getWorkCenterId, workCenterIds)
                    .list().stream()
                    .map(com.yelink.dfs.entity.manufacture.ProductionLineEntity::getModelId)
                    .distinct()
                    .collect(Collectors.toList());
            List<Integer> lineRelevanceIds = workCenterLineRelevanceService.lambdaQuery().in(WorkCenterLineRelevanceEntity::getWorkCenterId, workCenterIds)
                    .list().stream()
                    .map(WorkCenterLineRelevanceEntity::getLineId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(lineRelevanceIds)) {
                List<Integer> lineModelRelevanceIds = productionLineService.lambdaQuery().in(com.yelink.dfs.entity.manufacture.ProductionLineEntity::getProductionLineId, lineRelevanceIds)
                        .list().stream()
                        .map(com.yelink.dfs.entity.manufacture.ProductionLineEntity::getModelId).collect(Collectors.toList());
                lineModelIds.addAll(lineModelRelevanceIds);
            }
            // 查询设备模型(主资源和关联资源都要查出)
            List<Integer> deviceIds = workCenterDeviceService.lambdaQuery().in(WorkCenterDeviceEntity::getWorkCenterId, workCenterIds)
                    .list().stream()
                    .map(WorkCenterDeviceEntity::getDeviceId).collect(Collectors.toList());
            deviceIds.addAll(workCenterDeviceRelevanceService.lambdaQuery().in(WorkCenterDeviceRelevanceEntity::getWorkCenterId, workCenterIds)
                    .list().stream()
                    .map(WorkCenterDeviceRelevanceEntity::getDeviceId).collect(Collectors.toList()));
            if (CollectionUtils.isNotEmpty(deviceIds)) {
                deviceModelIds = deviceService.listByIds(deviceIds).stream().map(DeviceEntity::getModelId).distinct().collect(Collectors.toList());
            }
            // 查询班组模型(主资源和关联资源都要查出)
            List<Integer> teamIds = workCenterTeamService.lambdaQuery().in(WorkCenterTeamEntity::getWorkCenterId, workCenterIds)
                    .list().stream()
                    .map(WorkCenterTeamEntity::getTeamId).collect(Collectors.toList());
            teamIds.addAll(workCenterTeamRelevanceService.lambdaQuery().in(WorkCenterTeamRelevanceEntity::getWorkCenterId, workCenterIds)
                    .list().stream()
                    .map(WorkCenterTeamRelevanceEntity::getTeamId)
                    .distinct()
                    .collect(Collectors.toList()));
            if (CollectionUtils.isNotEmpty(teamIds)) {
                teamModelIds = teamService.listByIds(teamIds).stream().map(SysTeamEntity::getTeamType).collect(Collectors.toList());
            }
        }
        // 查询设备
        boolean isDeviceWorkCenter = Optional.ofNullable(selectDTO.getProductionBasicUnitTypeDTO())
                .map(dto -> WorkCenterTypeEnum.DEVICE.getCode().equals(dto.getWorkCenterType()))
                .orElse(false);
        List<ModelEntity> deviceModels = modelService.lambdaQuery()
                .eq(isDeviceWorkCenter, ModelEntity::getId, Optional.ofNullable(selectDTO.getProductionBasicUnitTypeDTO()).map(ProductionBasicUnitTypeDTO::getProductionBasicUnitTypeId).orElse(null))
                .in(CollectionUtils.isNotEmpty(deviceModelIds), ModelEntity::getId, deviceModelIds)
                .eq(ModelEntity::getType, ModelEnum.DEVICE.getType())
                .list();
        List<CommonType> deviceDtos = deviceModels.stream().map(o ->
                        CommonType.builder().id(o.getId()).type(WorkCenterTypeEnum.DEVICE.getCode()).name(o.getName()).build())
                .collect(Collectors.toList());
        CommonType deviceDto = CommonType.builder().type(WorkCenterTypeEnum.DEVICE.getCode()).name(WorkCenterTypeEnum.DEVICE.getName()).list(deviceDtos).build();
        // 通过生产资源查询的对象直接返回，无需查询其他数据
        if (isDeviceWorkCenter) {
            list.add(deviceDto);
            return list;
        }
        // 查询班组
        boolean isTeamWorkCenter = Optional.ofNullable(selectDTO.getProductionBasicUnitTypeDTO())
                .map(dto -> WorkCenterTypeEnum.TEAM.getCode().equals(dto.getWorkCenterType()))
                .orElse(false);
        List<TeamTypeDefEntity> teamTypeDefEntities = teamTypeDefService.lambdaQuery()
                .eq(isTeamWorkCenter, TeamTypeDefEntity::getId, Optional.ofNullable(selectDTO.getProductionBasicUnitTypeDTO()).map(ProductionBasicUnitTypeDTO::getProductionBasicUnitTypeId).orElse(null))
                .in(CollectionUtils.isNotEmpty(teamModelIds), TeamTypeDefEntity::getId, teamModelIds)
                .list();
        List<CommonType> teamDtos = teamTypeDefEntities.stream().map(o ->
                        CommonType.builder().id(o.getId()).type(WorkCenterTypeEnum.TEAM.getCode()).code(o.getTypeDefCode()).name(o.getTypeDefName()).build())
                .collect(Collectors.toList());
        CommonType teamDto = CommonType.builder().type(WorkCenterTypeEnum.TEAM.getCode()).name(WorkCenterTypeEnum.TEAM.getName()).list(teamDtos).build();
        // 通过生产资源查询的对象直接返回，无需查询其他数据
        if (isTeamWorkCenter) {
            list.add(teamDto);
            return list;
        }
        // 查询制造单元
        boolean isLineWorkCenter = Optional.ofNullable(selectDTO.getProductionBasicUnitTypeDTO())
                .map(dto -> WorkCenterTypeEnum.LINE.getCode().equals(dto.getWorkCenterType()))
                .orElse(false);
        List<ModelEntity> lineModels = modelService.lambdaQuery()
                .eq(isLineWorkCenter, ModelEntity::getId, Optional.ofNullable(selectDTO.getProductionBasicUnitTypeDTO()).map(ProductionBasicUnitTypeDTO::getProductionBasicUnitTypeId).orElse(null))
                .in(CollectionUtils.isNotEmpty(lineModelIds), ModelEntity::getId, lineModelIds)
                .eq(ModelEntity::getType, ModelEnum.LINE.getType()).list();
        List<CommonType> lineDtos = lineModels.stream().map(o ->
                        CommonType.builder().id(o.getId()).type(WorkCenterTypeEnum.LINE.getCode()).name(o.getName()).build())
                .collect(Collectors.toList());
        CommonType lineDto = CommonType.builder().type(WorkCenterTypeEnum.LINE.getCode()).name(WorkCenterTypeEnum.LINE.getName()).list(lineDtos).build();
        // 通过生产资源查询的对象直接返回，无需查询其他数据
        if (isLineWorkCenter) {
            list.add(lineDto);
            return list;
        }
        // 通过工作中心查询的对象，仅查询工作中心的主资源和关联资源即可
        if (CollectionUtils.isNotEmpty(workCenterIds)) {
            List<WorkCenterEntity> workCenterEntities = workCenterService.listByIds(workCenterIds);
            List<String> workCenterTypes = workCenterEntities.stream().map(WorkCenterEntity::getType).distinct().collect(Collectors.toList());
            workCenterTypes.addAll(workCenterEntities.stream().map(WorkCenterEntity::getRelevanceType).distinct().collect(Collectors.toList()));
            if (workCenterTypes.contains(WorkCenterTypeEnum.LINE.getCode())) {
                list.add(lineDto);
            }
            if (workCenterTypes.contains(WorkCenterTypeEnum.TEAM.getCode())) {
                list.add(teamDto);
            }
            if (workCenterTypes.contains(WorkCenterTypeEnum.DEVICE.getCode())) {
                list.add(deviceDto);
            }
            return list;
        }

        list.add(lineDto);
        list.add(deviceDto);
        list.add(teamDto);
        return list;
    }

    @Override
    public void bindOperator(WorkOrderOperatorDTO operatorDTO) {
        workOrderOperatorService.lambdaUpdate()
                .eq(WorkOrderOperatorEntity::getWorkOrderNumber, operatorDTO.getWorkOrderNumber())
                .eq(WorkOrderOperatorEntity::getOperatorType, operatorDTO.getOperatorType())
                .remove();
        if (CollectionUtils.isEmpty(operatorDTO.getOperators()) || StringUtils.isBlank(operatorDTO.getWorkOrderNumber())) {
            return;
        }
        List<WorkOrderOperatorEntity> operatorEntities = operatorDTO.getOperators().stream().map(o ->
                WorkOrderOperatorEntity.builder()
                        .workOrderNumber(operatorDTO.getWorkOrderNumber())
                        .operatorType(operatorDTO.getOperatorType())
                        .operator(o)
                        .build()).collect(Collectors.toList());
        workOrderOperatorService.saveBatch(operatorEntities);
    }

    @Override
    public WorkOrderOperatorDTO selectBindOperator(WorkOrderOperatorSelectDTO operatorDTO) {
        List<WorkOrderOperatorEntity> operatorEntities = workOrderOperatorService.lambdaQuery()
                .eq(WorkOrderOperatorEntity::getOperatorType, operatorDTO.getOperatorType())
                .eq(WorkOrderOperatorEntity::getWorkOrderNumber, operatorDTO.getWorkOrderNumber()).list();
        List<String> operators = operatorEntities.stream().map(WorkOrderOperatorEntity::getOperator).collect(Collectors.toList());
        return WorkOrderOperatorDTO.builder()
                .workOrderNumber(operatorDTO.getOperatorType())
                .operatorType(operatorDTO.getOperatorType())
                .operators(operators)
                .build();
    }

    /**
     * 绑定生产基本单元并刷新记录
     */
    private void bindProductBasicUnit(WorkOrderProductBasicUnitChangeDTO changeDTO, String workCenterType, Integer workCenterId, Date refreshInvestTime, String username) {
        List<Integer> bindProductBasicUnitIds = changeDTO.getProductBasicUnitIds();
        // 取最新的一条投产记录
        Map<Integer, WorkOrderBasicUnitInputRecordEntity> inputMap = basicUnitInputRecordService.lambdaQuery()
                .eq(WorkOrderBasicUnitInputRecordEntity::getWorkOrderNumber, changeDTO.getWorkOrderNumber())
                .orderByDesc(WorkOrderBasicUnitInputRecordEntity::getId)
                .list().stream()
                .collect(Collectors.toMap(WorkOrderBasicUnitInputRecordEntity::getProductionBasicUnitId, o -> o, (v1, v2) -> v1));
        List<WorkOrderBasicUnitRelationEntity> basicUnitRelationEntities = basicUnitRelationService.lambdaQuery()
                .eq(WorkOrderBasicUnitRelationEntity::getWorkOrderNumber, changeDTO.getWorkOrderNumber())
                .list();
        Map<Integer, WorkOrderBasicUnitRelationEntity> basicUnitMap = basicUnitRelationEntities.stream()
                .collect(Collectors.toMap(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitId, v -> v));
        // 将选择投产的生产基本单元改为投产，并刷新投产记录
        basicUnitRelationService.lambdaUpdate()
                .eq(WorkOrderBasicUnitRelationEntity::getWorkOrderNumber, changeDTO.getWorkOrderNumber())
                .in(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitId, bindProductBasicUnitIds)
                .set(WorkOrderBasicUnitRelationEntity::getIsProducing, true)
                .update();
        for (Integer productBasicUnitId : bindProductBasicUnitIds) {
            WorkOrderBasicUnitInputRecordEntity inputRecordEntity = inputMap.get(productBasicUnitId);
            WorkOrderBasicUnitRelationEntity basicUnitEntity = basicUnitMap.get(productBasicUnitId);
            // 如果投产记录为空或者最新的投产结束时间为空，则新增一条投产记录
            if (Objects.isNull(inputRecordEntity) || Objects.nonNull(inputRecordEntity.getEndTime())) {
                basicUnitInputRecordService.save(WorkOrderBasicUnitInputRecordEntity.builder()
                        .workOrderNumber(changeDTO.getWorkOrderNumber())
                        .workCenterId(workCenterId)
                        .workCenterType(workCenterType)
                        .productionBasicUnitId(productBasicUnitId)
                        .productionBasicUnitName(basicUnitEntity.getProductionBasicUnitName())
                        .startTime(refreshInvestTime)
                        .username(username)
                        .build());
            }
        }
        // 未绑定的生产基本单元改为非投产，并刷新投产记录
        List<Integer> notIncludeInvestUnitIds = basicUnitRelationEntities.stream()
                .map(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitId)
                .filter(productionBasicUnitId -> !bindProductBasicUnitIds.contains(productionBasicUnitId))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notIncludeInvestUnitIds)) {
            basicUnitRelationService.lambdaUpdate()
                    .eq(WorkOrderBasicUnitRelationEntity::getWorkOrderNumber, changeDTO.getWorkOrderNumber())
                    .in(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitId, notIncludeInvestUnitIds)
                    .set(WorkOrderBasicUnitRelationEntity::getIsProducing, false)
                    .update();
            for (Integer notIncludeInvestUnitId : notIncludeInvestUnitIds) {
                WorkOrderBasicUnitInputRecordEntity inputRecordEntity = inputMap.get(notIncludeInvestUnitId);
                // 仅对无投产结束时间的数据刷新投产结束时间
                if (Objects.isNull(inputRecordEntity) || Objects.nonNull(inputRecordEntity.getEndTime())) {
                    continue;
                }
                basicUnitInputRecordService.lambdaUpdate()
                        .eq(WorkOrderBasicUnitInputRecordEntity::getId, inputRecordEntity.getId())
                        .set(WorkOrderBasicUnitInputRecordEntity::getEndTime, refreshInvestTime)
                        .update();
            }
        }
    }

    private WorkOrderEntity pushDownToWorkOrder(Integer ruleId, CraftRoutePushWorkOrderVO.TargetOrderPushMaterialListVO pushMaterial,
                                                String username, Map<String, ProductOrderMaterialEntity> productOrderMaterialMap,
                                                WorkOrderService workOrderService, Boolean carryFile) {
        ProductOrderMaterialEntity productOrderMaterialEntity = productOrderMaterialMap.get(pushMaterial.getSourceOrderNumber());
        String relatedSaleOrderCode = productOrderMaterialEntity.getSaleOrderCode();
        // 获取前端配置的编码规则，拼接编码规则值，组装成生产工单号
        String workOrderNumber = getWorkOrderNumber(pushMaterial, ruleId, relatedSaleOrderCode);
        if (StringUtils.isBlank(workOrderNumber)) {
            throw new ResponseException(RespCodeEnum.CANNOT_GENERATE_WORK_ORDER_NUMBER);
        }
        List<SaleOrderVO> saleOrderEntities = new ArrayList<>();
        String customerCodes = null, customerNames = null, salesmanCodes = null, salesmanNames = null, salesmanMobiles = null;
        // 获取销售订单列表
        if (StringUtils.isNotBlank(relatedSaleOrderCode)) {
            // 可能关联多个销售订单
            List<String> saleOrderCodes = Arrays.asList(relatedSaleOrderCode.split(Constants.SEP));
            saleOrderEntities = extSaleOrderInterface.getPage(SaleOrderSelectOpenDTO.builder().saleOrderNumbers(saleOrderCodes).build()).getRecords();
            // 获取客户相关信息、销售员相关信息
            customerCodes = saleOrderEntities.stream().map(SaleOrderVO::getCustomerCode).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(Constant.SEP));
            customerNames = saleOrderEntities.stream().map(SaleOrderVO::getCustomerName).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(Constant.SEP));
            salesmanCodes = saleOrderEntities.stream().map(SaleOrderVO::getSalesmanCode).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(Constant.SEP));
            salesmanNames = saleOrderEntities.stream().map(SaleOrderVO::getSalesmanName).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(Constant.SEP));
            salesmanMobiles = saleOrderEntities.stream().map(SaleOrderVO::getSalesmanMobile).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(Constant.SEP));
        }
        ProductionLineService productionLineService = SpringUtil.getBean(ProductionLineService.class);
        ProductionLineEntity lineEntity = Objects.nonNull(pushMaterial.getLineId()) ? JacksonUtil.convertObject(productionLineService.getById(pushMaterial.getLineId()), ProductionLineEntity.class) : null;

        WorkCenterEntity workCenter = Objects.nonNull(pushMaterial.getWorkCenterId()) ? workCenterService.getById(pushMaterial.getWorkCenterId()) : null;
        Date now = new Date();
        // 如果单据类型未赋值，则需根据单据类型配置动态获取默认值
        OrderTypeInfoVO defaultOrderTypeVO = orderTypeConfigService.getDefaultOrderTypeCodeByCategoryCode(CategoryTypeEnum.WORK_ORDER.getTypeCode());

        WorkOrderEntity workOrderEntity = WorkOrderEntity.builder()
                .productOrderNumber(pushMaterial.getSourceOrderNumber())
                .saleOrderNumber(relatedSaleOrderCode)
                .relateOrderMaterialId(productOrderMaterialEntity.getRelateSaleOrderMaterialId())
                .workCenterId(pushMaterial.getWorkCenterId())
                .workCenterName(Objects.nonNull(workCenter) ? workCenter.getName() : null)
                .materialCheckType(Objects.isNull(lineEntity) ? null : lineEntity.getMaterialCheckType())
                .plannedWorkingHours(0.0)
                .approvalStatus(ApprovalStatusEnum.TO_BE_APPROVAL.getCode())
                .isPid(false)
                .supplierCode(pushMaterial.getSupplierCode())
                .supplierName(pushMaterial.getSupplierName())
                .type(ModelEnum.WORK_ORDER.getType())
                .state(WorkOrderStateEnum.CREATED.getCode())
                .workOrderNumber(workOrderNumber)
                .workOrderName(StringUtils.isBlank(pushMaterial.getWorkOrderName()) ? workOrderNumber : pushMaterial.getWorkOrderName())
                .pushTimes(pushMaterial.getPushTimes())
                .planQuantity(pushMaterial.getPlanQuantity())
                .materialCode(pushMaterial.getMaterialCode())
                .materialFields(JacksonUtil.convertObject(pushMaterial.getMaterialFields(), MaterialEntity.class))
                .customerMaterialCode(pushMaterial.getCustomerMaterialCode())
                .customerMaterialName(pushMaterial.getCustomerMaterialName())
                .customerSpecification(pushMaterial.getCustomerSpecification())
                .craftId(pushMaterial.getCraftId())
                .craftCode(pushMaterial.getCraftCode())
                .skuId(pushMaterial.getSkuId())
                .orderType(StringUtils.isNotBlank(pushMaterial.getOrderType()) ? pushMaterial.getOrderType() : defaultOrderTypeVO.getOrderType())
                .businessType(StringUtils.isNotBlank(pushMaterial.getBusinessType()) ? pushMaterial.getBusinessType() : defaultOrderTypeVO.getBusinessTypeCode())
                .plannedBatches(pushMaterial.getPlannedBatches())
                .actualBatches(pushMaterial.getActualBatches())
                .plansPerBatch(pushMaterial.getPlansPerBatch())
                .finishCount(0.0)
                .progress(0.0)
                .priority(pushMaterial.getPriority())
                .assignmentState(pushMaterial.getAssignmentState())
                .createDate(now)
                .updateDate(now)
                .createBy(username)
                .updateBy(username)
                .startDate(pushMaterial.getStartDate())
                .endDate(pushMaterial.getEndDate())
                .customerCode(customerCodes)
                .customerName(customerNames)
                .magNickname(salesmanNames)
                .magPhone(salesmanMobiles)
                .magName(salesmanCodes)
                .productCount(0.0)
                .pendentQuantity(pushMaterial.getPlanQuantity())
                .approver(username)
                .relevanceLineIds(pushMaterial.getRelevanceLineIds())
                .relevanceTeamIds(pushMaterial.getRelevanceTeamIds())
                .relevanceDeviceIds(pushMaterial.getRelevanceDeviceIds())
                .packageSchemeCode(pushMaterial.getPackageSchemeCode())
                .relatedProductOrderMaterialLineNumber(productOrderMaterialEntity.getLineNumber())
                .relatedSaleOrderMaterialLineNumber(productOrderMaterialEntity.getRelatedSaleOrderMaterialLineNumber())
                .workOrderExtendFieldOne(pushMaterial.getWorkOrderExtendFieldOne())
                .workOrderExtendFieldTwo(pushMaterial.getWorkOrderExtendFieldTwo())
                .workOrderExtendFieldThree(pushMaterial.getWorkOrderExtendFieldThree())
                .workOrderExtendFieldFour(pushMaterial.getWorkOrderExtendFieldFour())
                .workOrderExtendFieldFive(pushMaterial.getWorkOrderExtendFieldFive())
                .workOrderExtendFieldSix(pushMaterial.getWorkOrderExtendFieldSix())
                .workOrderExtendFieldSeven(pushMaterial.getWorkOrderExtendFieldSeven())
                .workOrderExtendFieldEight(pushMaterial.getWorkOrderExtendFieldEight())
                .workOrderExtendFieldNine(pushMaterial.getWorkOrderExtendFieldNine())
                .workOrderExtendFieldTen(pushMaterial.getWorkOrderExtendFieldTen())
                .workOrderMaterialExtendFieldOne(pushMaterial.getWorkOrderMaterialExtendFieldOne())
                .workOrderMaterialExtendFieldTwo(pushMaterial.getWorkOrderMaterialExtendFieldTwo())
                .workOrderMaterialExtendFieldThree(pushMaterial.getWorkOrderMaterialExtendFieldThree())
                .workOrderMaterialExtendFieldFour(pushMaterial.getWorkOrderMaterialExtendFieldFour())
                .workOrderMaterialExtendFieldFive(pushMaterial.getWorkOrderMaterialExtendFieldFive())
                .workOrderMaterialExtendFieldSix(pushMaterial.getWorkOrderMaterialExtendFieldSix())
                .workOrderMaterialExtendFieldSeven(pushMaterial.getWorkOrderMaterialExtendFieldSeven())
                .workOrderMaterialExtendFieldEight(pushMaterial.getWorkOrderMaterialExtendFieldEight())
                .workOrderMaterialExtendFieldNine(pushMaterial.getWorkOrderMaterialExtendFieldNine())
                .workOrderMaterialExtendFieldTen(pushMaterial.getWorkOrderMaterialExtendFieldTen())
                .businessUnitCode(pushMaterial.getBusinessUnitCode())
                .businessUnitName(pushMaterial.getBusinessUnitName())
                .build();
        // 设置生产基本单元id和隔离ID
        workOrderService.setProductionBasicUnitIdAndIsolationId(workOrderEntity);
        // 保存订单工单关联关系
        workOrderEntity.setProductOrderList(Stream.of(ProductOrderEntity.builder().productOrderId(productOrderMaterialEntity.getProductOrderId()).productOrderNumber(productOrderMaterialEntity.getProductOrderNumber()).build()).collect(Collectors.toList()));
        workOrderEntity.setSaleOrderList(JacksonUtil.convertArray(saleOrderEntities, SaleOrderEntity.class));
        // 插入到工单工序关联表
        if (StringUtils.isNotEmpty(pushMaterial.getCraftProcedureIds())) {
            List<CraftProcedureEntity> workOrderRelationCraftProcedures = craftProcedureService.listByIds(Arrays.asList(pushMaterial.getCraftProcedureIds().split(Constants.CROSSBAR)));
            workOrderEntity.setCraftProcedureEntities(workOrderRelationCraftProcedures);
        }
        // 更新到工单-生产基本单元关联表
        WorkOrderBasicUnitInsertDTO basicUnitInsertDTO = WorkOrderBasicUnitInsertDTO.builder()
                .workOrderNumber(workOrderEntity.getWorkOrderNumber())
                .productBasicUnits(JacksonUtil.convertArray(pushMaterial.getProductBasicUnits(), WorkOrderBasicUnitRelationInsertDTO.class))
                .build();
        basicUnitRelationService.batchSaveWorkOrderBasicUnits(Collections.singletonList(basicUnitInsertDTO));
        // 保存工单
        add(workOrderEntity);
        // 需要保存工单后才能获取工单id，插入工单附件和生产订单附件
        if (Boolean.TRUE.equals(carryFile)) {
            List<AppendixEntity> workOrderFiles = new ArrayList<>();
            if (StringUtils.isNotEmpty(pushMaterial.getCraftProcedureIds())) {
                List<String> craftProcedureIds = Arrays.asList(pushMaterial.getCraftProcedureIds().split(Constants.CROSSBAR));
                List<AppendixEntity> procedureFiles = procedureFileService.lambdaQuery().in(ProcedureFileEntity::getProcedureId, craftProcedureIds)
                        .list().stream()
                        .map(o -> AppendixEntity.builder().fileName(o.getName()).filePath(o.getFileUrl()).build())
                        .collect(Collectors.toList());
                workOrderFiles.addAll(procedureFiles);
            }
            List<AppendixEntity> productOrderFiles = appendixService.getFilesById(AppendixTypeEnum.PRODUCT_ORDER_APPENDIX.getCode(), String.valueOf(productOrderMaterialEntity.getProductOrderId()), null);
            workOrderFiles.addAll(productOrderFiles);

            appendixService.addAppendix(workOrderFiles, AppendixTypeEnum.WORKORDER_APPENDIX, String.valueOf(workOrderEntity.getWorkOrderId()), workOrderEntity.getWorkOrderNumber(), username);
        }
        return workOrderEntity;
    }

    /**
     * 解绑生产基本单元并刷新记录
     * （占用其他工单的生产基本单元，需要设置为未投产，并刷新对应的投产记录）
     */
    @Override
    public void unbindProductBasic(WorkOrderProductBasicUnitChangeDTO changeDTO, String workCenterType, Date refreshInvestTime) {
        List<Integer> bindProductBasicUnitIds = changeDTO.getProductBasicUnitIds();
        Map<Integer, List<WorkOrderBasicUnitInputRecordEntity>> inputMap = basicUnitInputRecordService.lambdaQuery()
                .ne(WorkOrderBasicUnitInputRecordEntity::getWorkOrderNumber, changeDTO.getWorkOrderNumber())
//                .eq(WorkOrderBasicUnitInputRecordEntity::getWorkCenterId, workCenterId)
                .eq(WorkOrderBasicUnitInputRecordEntity::getWorkCenterType, workCenterType)
                .in(WorkOrderBasicUnitInputRecordEntity::getProductionBasicUnitId, bindProductBasicUnitIds)
                .list().stream()
                .collect(Collectors.groupingBy(WorkOrderBasicUnitInputRecordEntity::getProductionBasicUnitId));
        basicUnitRelationService.lambdaUpdate()
                .ne(WorkOrderBasicUnitRelationEntity::getWorkOrderNumber, changeDTO.getWorkOrderNumber())
//                .eq(WorkOrderBasicUnitRelationEntity::getWorkCenterId, workCenterId)
                .eq(WorkOrderBasicUnitRelationEntity::getWorkCenterType, workCenterType)
                .in(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitId, bindProductBasicUnitIds)
                .set(WorkOrderBasicUnitRelationEntity::getIsProducing, false)
                .update();
        for (Integer productBasicUnitId : bindProductBasicUnitIds) {
            List<WorkOrderBasicUnitInputRecordEntity> inputRecordEntities = inputMap.get(productBasicUnitId);
            if (CollectionUtils.isEmpty(inputRecordEntities)) {
                continue;
            }
            List<Integer> ids = inputRecordEntities.stream().map(WorkOrderBasicUnitInputRecordEntity::getId).collect(Collectors.toList());
            basicUnitInputRecordService.lambdaUpdate()
                    .in(WorkOrderBasicUnitInputRecordEntity::getId, ids)
                    .set(WorkOrderBasicUnitInputRecordEntity::getEndTime, refreshInvestTime)
                    .update();
        }
    }

    public void add(WorkOrderEntity workOrderEntity) {
        WorkOrderService workOrderService = SpringUtil.getBean(WorkOrderService.class);
        //工单编号判断去重
        Long count = workOrderService.lambdaQuery().eq(WorkOrderEntity::getWorkOrderNumber, workOrderEntity.getWorkOrderNumber()).count();
        if (count > 0) {
            throw new ResponseException(RespCodeEnum.WORK_ORDER_CODE_DUPLICATE.getMsgDes());
        }
        workOrderEntity.setAssignmentState(AssignmentStateEnum.TO_BE_ASSIGNED.getType());
        workOrderEntity.setFinishCount(0.0);
        workOrderEntity.setProgress(0.0);
        workOrderEntity.setProductCount(0.0);
        //不是批次物料直接赋值为0，
        workOrderEntity.setPlannedBatches(Boolean.FALSE.equals(workOrderEntity.getMaterialFields().getIsBatchMag()) ? 0 : workOrderEntity.getPlannedBatches() == null ? 1 : workOrderEntity.getPlannedBatches());
        workOrderEntity.setPlansPerBatch(Boolean.FALSE.equals(workOrderEntity.getMaterialFields().getIsBatchMag()) ? 0 : workOrderEntity.getPlansPerBatch() == null ? workOrderEntity.getPlanQuantity() : workOrderEntity.getPlansPerBatch());
        workOrderEntity.setPendentQuantity(workOrderEntity.getPlanQuantity());
        workOrderEntity.setWorkOrderId(null);
        Date createTime = Optional.ofNullable(workOrderEntity.getCreateDate()).orElse(Optional.ofNullable(workOrderEntity.getFakerTime()).orElse(new Date()));
        workOrderEntity.setCreateDate(createTime);
        workOrderEntity.setUpdateDate(createTime);

        // 如果需要审批，则缺省为待审核状态
        if (approveConfigService.getConfigByCode(ApproveModuleEnum.WORK_ORDER.getCode())) {
            workOrderEntity.setApprovalStatus(ApprovalStatusEnum.TO_BE_SUBMIT.getCode());
        }
        // 计算计划工时
        workOrderEntity.setPlannedWorkingHours(workOrderService.calculatePlannedWorkHours(workOrderEntity));
        // 设置生产基本单元id和隔离ID
        workOrderService.setProductionBasicUnitIdAndIsolationId(workOrderEntity);
        save(workOrderEntity);
        //生产工单计划
//        workOrderPlanService.saveWorkOrderPlan(workOrderEntity);
        // 关联多个订单
        workOrderService.saveOrderRelation(workOrderEntity);
        // 插入到工单工序关联表
        workOrderProcedureRelationService.insertWorkOrderProcedureRelation(workOrderEntity);
        // 保存工单投产班组成员信息
        workOrderTeamService.add(workOrderEntity);
        // 保存工单关联资源
        workOrderService.saveRelevanceResource(workOrderEntity);
        // 更新产线工单关联关系
        workOrderService.updateProductLineRelation(workOrderEntity);
    }


    /**
     * 获取前端配置的编码规则，拼接编码规则值，组装成生产工单号
     */
    private String getWorkOrderNumber(CraftRoutePushWorkOrderVO.TargetOrderPushMaterialListVO targetOrderPushMaterialListVO,
                                      Integer ruleId, String relatedSaleOrderCode) {
        if (StringUtils.isBlank(targetOrderPushMaterialListVO.getPrefixDetail())) {
            return getSysWorkOrderNum(1);
        }
        // 拼接编码规则值，组装成生产工单号
        Map<String, String> relatedMap = new HashMap<>(8);
        relatedMap.put(AutoIncrementConfigureTypeEnum.PRODUCT_ORDER_ONLY_PUSH_DOWN.getCode(), targetOrderPushMaterialListVO.getSourceOrderNumber());
        relatedMap.put(AutoIncrementConfigureTypeEnum.SALE_ORDER_ONLY_PUSH_DOWN.getCode(), relatedSaleOrderCode);
        List<RuleDetailDTO> ruleDetailDTOs = JSONArray.parseArray(targetOrderPushMaterialListVO.getPrefixDetail(), RuleDetailDTO.class);
        NumberCodeDTO numberCodeDTO = numberRuleService.generateRules(ruleId, ruleDetailDTOs, relatedMap);
        String workOrderNumber = numberCodeDTO.getCode();

        // 编码规则有自动生成序号的，seq加1
        ruleSeqService.updateSeqEntity(relatedMap, ruleId, false);
        return workOrderNumber;
    }

    public synchronized String getSysWorkOrderNum(Integer i) {
        WorkOrderService workOrderService = SpringUtil.getBean(WorkOrderService.class);
        String workOrderNumber = codeFactory.getOrderNumber(workPropertise.getWorkOrderHeader());
        Long count = workOrderService.lambdaQuery().eq(WorkOrderEntity::getWorkOrderNumber, workOrderNumber).count();
        if (count > 0) {
            i++;
            if (i == 30) {
                throw new ResponseException(RespCodeEnum.ORDER_NUMBER_EXCEED_THIRTY_QUANTITY);
            }
            workOrderNumber = getSysWorkOrderNum(i);
        }
        return workOrderNumber;
    }

    /**
     * 组转成工艺工序数据
     */
    private void groupConversionWorkOrderProcedureList(List<WorkOrderProcedureDTO> workOrderProcedures,
                                                       Map<Integer, List<CraftProcedureEntity>> craftProcedureMapByCraftId,
                                                       Map<String, MaterialEntity> materialEntityMap,
                                                       ActualPushDownConfigDTO pushDownConfig,
                                                       List<CraftRoutePushWorkOrderVO.TargetOrderPushMaterialListVO> retList,
                                                       Map<Integer, CraftEntity> craftMap) {
        List<FieldMappingEntity> fieldMappingEntities = fieldMappingService.getFieldMappingList(FieldMappingSelectDTO.builder()
                .upstreamOrderType(Constant.PRODUCT_ORDER)
                .downstreamOrderType(Constant.WORK_ORDER)
                .build());
        Date now = new Date();
        Map<String, ProductOrderEntity> productOrderMap = new HashMap<>();
        ProductOrderEntity productOrderEntity = null;

        for (WorkOrderProcedureDTO workOrderProcedure : workOrderProcedures) {
            Date startDate = null;
            Date endDate = null;
            // 获取关联的生产订单
            if (Objects.nonNull(productOrderMap.get(workOrderProcedure.getSourceOrderNumber()))) {
                productOrderEntity = productOrderMap.get(workOrderProcedure.getSourceOrderNumber());
            } else {
                productOrderEntity = extProductOrderInterface.selectProductOrderByNumber(ProductOrderDetailDTO.builder().productOrderNumber(workOrderProcedure.getSourceOrderNumber()).build());
                productOrderMap.put(workOrderProcedure.getSourceOrderNumber(), productOrderEntity);
            }
            ProductOrderMaterialEntity productOrderMaterialEntity = productOrderEntity.getProductOrderMaterial();
            // 根据下推配置判断是否需要自动设置计划时间
            if (pushDownConfig.getEnableAutoFillPlanTime()) {
                // 优先取timeDTO里的，说明有明确传递的时间，没有则默认取计划生产开始时间和计划生产结束时间
                startDate = Objects.nonNull(workOrderProcedure.getTimeDTO()) ? workOrderProcedure.getTimeDTO().getStartDate() : null;
                endDate = Objects.nonNull(workOrderProcedure.getTimeDTO()) ? workOrderProcedure.getTimeDTO().getEndDate() : null;
                if (Objects.isNull(startDate)) {
                    startDate = productOrderMaterialEntity.getPlanProductStartTime() == null ? now : productOrderMaterialEntity.getPlanProductStartTime();
                    endDate = productOrderMaterialEntity.getPlanProductEndTime() == null ? DateUtil.addSec(now, 1) : productOrderMaterialEntity.getPlanProductEndTime();
                }
            }
            // 下推次数
            int pushTimes = getProductOrderPushTimes(productOrderMaterialEntity.getProductOrderId());

            for (CraftProcedureEntity createWorkOrderCraftProcedure : workOrderProcedure.getCreateWorkOrderCraftProcedures()) {
                // 获取工艺路线
                List<CraftProcedureEntity> craftProcedureEntities = craftProcedureMapByCraftId.get(createWorkOrderCraftProcedure.getCraftId());
                Map<Integer, String> craftProcedureNameMap = craftProcedureEntities.stream().collect(Collectors.toMap(CraftProcedureEntity::getId, CraftProcedureEntity::getProcedureName));
                // 根据下推配置判断是否需要过滤
                MaterialEntity materialEntity = materialEntityMap.get(createWorkOrderCraftProcedure.getMaterialCode());
                if (pushDownConfigService.isNeedFilter(pushDownConfig, materialEntity)) {
                    continue;
                }
                // 获取物料的特征参数
                materialEntity.setAuxiliaryAttrEntities(materialAuxiliaryAttrService.lambdaQuery().eq(MaterialAuxiliaryAttrEntity::getMaterialCode, materialEntity.getCode()).list());
                materialEntity.setSkuEntity(skuService.getById(productOrderMaterialEntity.getSkuId()));
                // 获取工艺工序名称
                List<Integer> craftProcedureId = Arrays.stream(createWorkOrderCraftProcedure.getCraftProcedureIds().split(Constant.CROSSBAR))
                        .map(Integer::valueOf)
                        .collect(Collectors.toList());
                String craftProcedureNames = craftProcedureId.stream().map(craftProcedureNameMap::get).filter(Objects::nonNull).collect(Collectors.joining(com.yelink.dfscommon.constant.Constant.RIGHT_ARROW));

                List<CommonType> suppliers = new ArrayList<>();
                if (StringUtils.isNotBlank(createWorkOrderCraftProcedure.getOutSourcingSupplierCode())) {
                    String[] supplierCodes = createWorkOrderCraftProcedure.getOutSourcingSupplierCode().split(Constants.SEP);
                    String[] supplierNames = createWorkOrderCraftProcedure.getOutSourcingSupplier().split(Constants.SEP);
                    for (int i = 0; i < supplierCodes.length; i++) {
                        suppliers.add(CommonType.builder().code(supplierCodes[i]).name(supplierNames[i]).build());
                    }
                }
                Integer workCenterId = null;
                List<com.yelink.dfs.entity.manufacture.ProductionLineEntity> productionLineEntities = new ArrayList<>();
                List<SysTeamEntity> teamEntities = new ArrayList<>();
                List<DeviceEntity> deviceEntities = new ArrayList<>();
                List<com.yelink.dfs.entity.manufacture.ProductionLineEntity> relatedProductionLineEntities = new ArrayList<>();
                List<SysTeamEntity> relatedTeamEntities = new ArrayList<>();
                List<DeviceEntity> relatedDeviceEntities = new ArrayList<>();
                if (StringUtils.isNotEmpty(createWorkOrderCraftProcedure.getWorkCenterNames())) {
                    String workCenterName = createWorkOrderCraftProcedure.getWorkCenterNames().split(Constant.SEP)[0];
                    WorkCenterEntity workCenter = workCenterService.lambdaQuery().eq(WorkCenterEntity::getName, workCenterName).one();
                    workCenterId = workCenter.getId();
                    // 根据工作中心类型调用不同的基本生产单元的列表接口，给前端使用
                    if (workCenter.getType().equals(WorkCenterTypeEnum.LINE.getCode())) {
                        productionLineEntities = workCenterService.listLineByCenterId(workCenterId, false, null, null);
                    } else if (workCenter.getType().equals(WorkCenterTypeEnum.TEAM.getCode())) {
                        teamEntities = workCenterService.listTeamByCenterId(workCenterId, false, null);
                    } else {
                        String craftProcedureIds = String.join(Constants.SEP, createWorkOrderCraftProcedure.getCraftProcedureIds().split(Constant.CROSSBAR));
                        deviceEntities = workCenterService.listDeviceByCenterId(workCenterId, false, null, craftProcedureIds);
                    }
                    // 关联资源
                    if (Objects.nonNull(workCenter.getRelevanceType())) {
                        workCenterId = workCenter.getId();
                        // 根据工作中心类型调用不同的基本生产单元的列表接口，给前端使用
                        if (workCenter.getRelevanceType().equals(WorkCenterTypeEnum.LINE.getCode())) {
                            relatedProductionLineEntities = workCenterService.getRelevanceLineForWorkOrder(workCenterId, null);
                        } else if (workCenter.getRelevanceType().equals(WorkCenterTypeEnum.TEAM.getCode())) {
                            relatedTeamEntities = workCenterService.getRelevanceTeamForWorkOrder(workCenterId);
                        } else if (workCenter.getRelevanceType().equals(WorkCenterTypeEnum.DEVICE.getCode())) {
                            relatedDeviceEntities = workCenterService.getRelevanceDeviceForWorkOrder(workCenterId, null);
                        }
                    }
                }
                // 如果派工状态为空，获取派工的业务配置
                FullPathCodeDTO dto = FullPathCodeDTO.builder()
                        .fullPathCode(ConfigConstant.WORK_ORDER_ASSIGNMENT_CONFIG).build();
                WorkOrderAssignmentConfigDTO config = businessConfigService.getValueDto(dto, WorkOrderAssignmentConfigDTO.class);
                // 根据生产订单的业务类型获取工单可选的业务类型以及单据类型
                List<BusinessTypeListVO> enableBusinessTypeListVOS = orderTypeConfigService.getOrderTypeListByOrderCategory(OrderTypeSelectDTO.builder()
                        .showType(OrderTypeShowTypeEnum.SHOW_ENABLE.getCode())
                        .categoryCode(CategoryTypeEnum.WORK_ORDER.getTypeCode())
                        .build());
                // 判断生产订单是不是试产订单的业务类型下的单据类型，如果是则默认拿试产工单的业务类型下的单据类型
                List<BusinessTypeListVO> vos = orderTypeConfigService.getOrderTypeListByBusinessType(OrderTypeSelectDTO.builder()
                        .businessCode(BusinessTypeEnum.TEST_PRODUCT_ORDER.getTypeCode())
                        .showType(OrderTypeShowTypeEnum.SHOW_ALL.getCode())
                        .build());
                List<String> businessCodes = vos.stream().map(BusinessTypeListVO::getBusinessTypeCode).collect(Collectors.toList());
                OrderTypeInfoVO defaultOrderType = null;
                if (businessCodes.contains(productOrderEntity.getType())) {
                    defaultOrderType = orderTypeConfigService.getDefaultOrderTypeCodeByBusinessCode(BusinessTypeEnum.TEST_WORK_ORDER.getTypeCode());
                    defaultOrderType.setIsEdit(false);
                    enableBusinessTypeListVOS = enableBusinessTypeListVOS.stream()
                            .filter(vo -> vo.getBusinessTypeCode().equals(BusinessTypeEnum.TEST_WORK_ORDER.getTypeCode()))
                            .collect(Collectors.toList());
                } else {
                    // 如果不是试产订单，则默认拿默认业务类型下的默认单据类型
                    defaultOrderType = orderTypeConfigService.getDefaultOrderTypeCodeByCategoryCode(CategoryTypeEnum.WORK_ORDER.getTypeCode());
                    defaultOrderType.setIsEdit(true);
                    enableBusinessTypeListVOS = enableBusinessTypeListVOS.stream()
                            .filter(vo -> !vo.getBusinessTypeCode().equals(BusinessTypeEnum.TEST_WORK_ORDER.getTypeCode()))
                            .collect(Collectors.toList());
                }
                try {
                    // 组转对象
                    retList.add(
                            CraftRoutePushWorkOrderVO.TargetOrderPushMaterialListVO.builder()
                                    .sourceOrderMaterialId(workOrderProcedure.getSourceOrderMaterialId())
                                    .sourceOrderNumber(workOrderProcedure.getSourceOrderNumber())
                                    .sourceOrderMaterialCode(workOrderProcedure.getSourceOrderMaterialCode())
                                    .sourceOrderType(pushDownConfig.getSourceOrderType())
                                    .targetOrderType(pushDownConfig.getTargetOrderType())
                                    .productOrderNumber(workOrderProcedure.getSourceOrderNumber())
                                    .saleOrderNumber(productOrderMaterialEntity.getSaleOrderCode())
                                    .pushTimes(pushTimes)
                                    .craftCode(createWorkOrderCraftProcedure.getCraftCode())
                                    .craftId(createWorkOrderCraftProcedure.getCraftId())
                                    .craftEntities(JacksonUtil.convertArray(Collections.singletonList(craftMap.get(createWorkOrderCraftProcedure.getCraftId())), CraftVO.class))
                                    .craftProcedureIds(createWorkOrderCraftProcedure.getCraftProcedureIds())
                                    .craftProcedureNames(craftProcedureNames)
                                    .craftProcedureEntities(JacksonUtil.convertArray(craftProcedureEntities, CraftProcedureVO.class))
                                    .productionLineEntities(JacksonUtil.convertArray(productionLineEntities, ProductionLineVO.class))
                                    .deviceEntities(JacksonUtil.convertArray(deviceEntities, DeviceVO.class))
                                    .teamEntities(JacksonUtil.convertArray(teamEntities, TeamVO.class))
                                    .relatedProductionLineEntities(JacksonUtil.convertArray(relatedProductionLineEntities, ProductionLineVO.class))
                                    .relatedDeviceEntities(JacksonUtil.convertArray(relatedDeviceEntities, DeviceVO.class))
                                    .relatedTeamEntities(JacksonUtil.convertArray(relatedTeamEntities, TeamVO.class))
                                    .materialCode(createWorkOrderCraftProcedure.getMaterialCode())
                                    .materialName(materialEntity.getName())
                                    .materialFields(JacksonUtil.convertObject(materialEntity, com.yelink.dfscommon.entity.dfs.MaterialEntity.class))
                                    .workCenterId(workCenterId)
                                    .workCenterNames(createWorkOrderCraftProcedure.getWorkCenterNames())
                                    .workCenterEntities(workCenterService.listByIds(Collections.singletonList(workCenterId)))
                                    .isSubContractingOperation(createWorkOrderCraftProcedure.getIsSubContractingOperation())
                                    .suppliers(suppliers)
                                    .orderType(defaultOrderType.getOrderType())
                                    .businessType(defaultOrderType.getBusinessTypeCode())
                                    .businessTypeListVOS(enableBusinessTypeListVOS)
                                    .isEdit(defaultOrderType.getIsEdit())
                                    .sourceOrderPushDownQuantity(workOrderProcedure.getSourceOrderPushDownQuantity())
                                    // 工单的计划数量 = 下推数量 * 换算系数（使用场景：电池组装）
                                    .planQuantity(MathUtil.mulToKeepTwoDecimalPlaces(workOrderProcedure.getSourceOrderPushDownQuantity(), createWorkOrderCraftProcedure.getConversionFactor()))
                                    .supplierCode(createWorkOrderCraftProcedure.getOutSourcingSupplierCode())
                                    .supplierName(createWorkOrderCraftProcedure.getOutSourcingSupplier())
                                    .customerCode(productOrderEntity.getCustomerCode())
                                    .customerName(productOrderEntity.getCustomerName())
                                    .customerMaterialCode(productOrderMaterialEntity.getCustomerMaterialCode())
                                    .customerMaterialName(productOrderMaterialEntity.getCustomerMaterialName())
                                    .customerSpecification(productOrderMaterialEntity.getCustomerSpecification())
                                    .skuId(productOrderMaterialEntity.getSkuId())
                                    .plannedBatches(!materialEntity.getIsBatchMag() ? 0 : productOrderMaterialEntity.getPlannedBatches())
                                    .actualBatches(productOrderMaterialEntity.getActualBatches())
                                    .plansPerBatch(Boolean.FALSE.equals(materialEntity.getIsBatchMag()) ? 0 : productOrderMaterialEntity.getPlansPerBatch())
                                    .startDate(startDate)
                                    .endDate(endDate)
                                    .assignmentState(Integer.valueOf(pushDownConfig.getTargetOrderState()).equals(WorkOrderStateEnum.RELEASED.getCode()) ? config.getAssignmentState() : AssignmentStateEnum.TO_BE_ASSIGNED.getType())
                                    // 优先级去生产订单的优先级
                                    .priority(Objects.nonNull(productOrderMaterialEntity.getPriority()) ? productOrderMaterialEntity.getPriority() : PriorityTypeEnum.NORMAL.getName())
                                    .packageSchemeCode(productOrderMaterialEntity.getPackageSchemeCode())
                                    .workOrderExtendFieldOne(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderExtendFieldOne", productOrderEntity, ProductOrderEntity.class))
                                    .workOrderExtendFieldTwo(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderExtendFieldTwo", productOrderEntity, ProductOrderEntity.class))
                                    .workOrderExtendFieldThree(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderExtendFieldThree", productOrderEntity, ProductOrderEntity.class))
                                    .workOrderExtendFieldFour(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderExtendFieldFour", productOrderEntity, ProductOrderEntity.class))
                                    .workOrderExtendFieldFive(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderExtendFieldFive", productOrderEntity, ProductOrderEntity.class))
                                    .workOrderExtendFieldSix(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderExtendFieldSix", productOrderEntity, ProductOrderEntity.class))
                                    .workOrderExtendFieldSeven(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderExtendFieldSeven", productOrderEntity, ProductOrderEntity.class))
                                    .workOrderExtendFieldEight(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderExtendFieldEight", productOrderEntity, ProductOrderEntity.class))
                                    .workOrderExtendFieldNine(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderExtendFieldNine", productOrderEntity, ProductOrderEntity.class))
                                    .workOrderExtendFieldTen(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderExtendFieldTen", productOrderEntity, ProductOrderEntity.class))
                                    .workOrderMaterialExtendFieldOne(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderMaterialExtendFieldOne", productOrderMaterialEntity, ProductOrderMaterialEntity.class))
                                    .workOrderMaterialExtendFieldTwo(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderMaterialExtendFieldTwo", productOrderMaterialEntity, ProductOrderMaterialEntity.class))
                                    .workOrderMaterialExtendFieldThree(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderMaterialExtendFieldThree", productOrderMaterialEntity, ProductOrderMaterialEntity.class))
                                    .workOrderMaterialExtendFieldFour(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderMaterialExtendFieldFour", productOrderMaterialEntity, ProductOrderMaterialEntity.class))
                                    .workOrderMaterialExtendFieldFive(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderMaterialExtendFieldFive", productOrderMaterialEntity, ProductOrderMaterialEntity.class))
                                    .workOrderMaterialExtendFieldSix(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderMaterialExtendFieldSix", productOrderMaterialEntity, ProductOrderMaterialEntity.class))
                                    .workOrderMaterialExtendFieldSeven(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderMaterialExtendFieldSeven", productOrderMaterialEntity, ProductOrderMaterialEntity.class))
                                    .workOrderMaterialExtendFieldEight(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderMaterialExtendFieldEight", productOrderMaterialEntity, ProductOrderMaterialEntity.class))
                                    .workOrderMaterialExtendFieldNine(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderMaterialExtendFieldNine", productOrderMaterialEntity, ProductOrderMaterialEntity.class))
                                    .workOrderMaterialExtendFieldTen(ColumnUtil.getFieldMappingValue(fieldMappingEntities, "workOrderMaterialExtendFieldTen", productOrderMaterialEntity, ProductOrderMaterialEntity.class))
                                    .businessUnitCode(workOrderProcedure.getBusinessUnitCode())
                                    .businessUnitName(workOrderProcedure.getBusinessUnitName())
                                    .dealId(workOrderProcedure.getDealId())
                                    .build()
                    );
                } catch (Exception e) {
                    log.error("字段映射异常");
                    throw new ResponseException("字段映射异常");
                }
            }
        }
    }

    /**
     * 获取 生产订单下推批数
     */
    private int getProductOrderPushTimes(Integer productOrderId) {
        // 获取生产订单关联的生产工单
        List<OrderWorkOrderEntity> workOrderRelations = orderWorkOrderService.listOrderWorkOrderRelations(Collections.singletonList(productOrderId), OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode());
        if (CollectionUtils.isEmpty(workOrderRelations)) {
            return 1;
        } else {
            List<Integer> workOrderIds = workOrderRelations.stream().map(OrderWorkOrderEntity::getWorkOrderId).collect(Collectors.toList());
            Optional<Integer> pushTimeMax = this.lambdaQuery().in(WorkOrderEntity::getWorkOrderId, workOrderIds)
                    .list().stream().map(WorkOrderEntity::getPushTimes)
                    .max(Comparator.comparingInt(o -> o));
            return pushTimeMax.orElse(0) + 1;
        }
    }

    /**
     * 根据配置获取工序绑定的物料
     */
    private static void setCraftProcedureMaterialCode(List<WorkOrderProcedureDTO> workOrderProcedures,
                                                      Map<Integer, CraftEntity> craftMap,
                                                      Map<String, ProcedureMaterialEntity> procedureMaterialMap,
                                                      ActualPushDownConfigDTO pushDownConfig) {
        for (WorkOrderProcedureDTO workOrderProcedure : workOrderProcedures) {
            for (CraftProcedureEntity createWorkOrderCraftProcedure : workOrderProcedure.getCreateWorkOrderCraftProcedures()) {
                // 获取工艺路线
                CraftEntity craftEntity = craftMap.get(createWorkOrderCraftProcedure.getCraftId());
                String materialCode = craftEntity.getMaterialCode();
                if (StringUtils.isBlank(materialCode)) {
                    // 默认工艺（工艺模板）没有关联物料，取原始下推的物料
                    materialCode = createWorkOrderCraftProcedure.getMaterialCode();
                }
                // 物料由配置决定，如果 `物料选择` 为 成品，则维持原逻辑，即订单物料。如果是工序物料，则取订单物料的工艺工序里的工序物料
                if (pushDownConfig.getCraftSplitType().equals(CraftSplitTypeEnum.PROCEDURE.getTypeCode()) && pushDownConfig.getMaterialChoose().equals(WorkOrderMaterialChooseEnum.PROCEDURE_MATERIAL.getCode())) {
                    // 获取工序物料
                    String key = createWorkOrderCraftProcedure.getCraftId() + Constants.UNDER_LINE + createWorkOrderCraftProcedure.getId();
                    ProcedureMaterialEntity procedureMaterialEntity = procedureMaterialMap.get(key);
                    if (Objects.isNull(procedureMaterialEntity)) {
                        throw new ResponseException(RespCodeEnum.NOT_PUSH_DOWN_BECAUSE_HAS_NOT_PROCEDURE_MATERIAL.fmtDes(craftEntity.getCraftCode(), createWorkOrderCraftProcedure.getProcedureName()));
                    }
                    materialCode = procedureMaterialEntity.getMaterialCode();
                }
                createWorkOrderCraftProcedure.setMaterialCode(materialCode);
                createWorkOrderCraftProcedure.setCraftCode(craftEntity.getCraftCode());
                // 恒秋没有在工艺上加skuId
                createWorkOrderCraftProcedure.setSkuId(Constants.SKU_ID_DEFAULT_VAL);
            }
        }
    }

    /**
     * 获取工单工序列表
     */
    private WorkOrderProcedureDTO getWorkOrderProcedureByProcedure(DefaultCraftPushDownDTO craftPushDownDTO) {
        if (Objects.isNull(craftPushDownDTO.getCraftId())) {
            return new WorkOrderProcedureDTO(craftPushDownDTO);
        }
        // 获取该物料工作中心不为空的工艺工序
        List<CraftProcedureEntity> workCenterNotEmptyCraftProcedureList = listWorkCenterNotEmptyCraftProcedure(craftPushDownDTO);
        if (CollectionUtils.isEmpty(workCenterNotEmptyCraftProcedureList)) {
            return new WorkOrderProcedureDTO(craftPushDownDTO);
        }
        // 直接按工序拆分, 每个工艺工序创建一个生产工单
        List<CraftProcedureEntity> createWorkOrderCraftProcedures = new ArrayList<>();

        // 创建的工单需要绑定的工序
        Map<Integer, List<CraftProcedureEntity>> idRelationCraftProceduresMap = new HashMap<>(8);

        // 如果为工序日计划需特殊处理
        if (StringUtils.isNotBlank(craftPushDownDTO.getCraftProcedureId())) {
            CraftProcedureEntity craftProcedure = workCenterNotEmptyCraftProcedureList.get(0);
            idRelationCraftProceduresMap.put(craftProcedure.getId(), workCenterNotEmptyCraftProcedureList);
            craftProcedure.setCraftProcedureIds(workCenterNotEmptyCraftProcedureList.stream().map(CraftProcedureEntity::getId).map(String::valueOf).collect(Collectors.joining(Constant.CROSSBAR)));
            createWorkOrderCraftProcedures.add(craftProcedure);
        }
        // 普通下推
        else {
            for (CraftProcedureEntity craftProcedure : workCenterNotEmptyCraftProcedureList) {
                // 插入工艺工序，用于下推时工单绑定工序
                idRelationCraftProceduresMap.put(craftProcedure.getId(), Stream.of(craftProcedure).collect(Collectors.toList()));
                craftProcedure.setCraftProcedureIds(String.valueOf(craftProcedure.getId()));
                createWorkOrderCraftProcedures.add(craftProcedure);
            }
        }
        return new WorkOrderProcedureDTO(craftPushDownDTO, createWorkOrderCraftProcedures, idRelationCraftProceduresMap);
    }

    /**
     * 按工作中心下推的新方案
     * 将工作中心类型相同的数据归属一个工单，不连续也要归属为一个工单
     */
    private WorkOrderProcedureDTO getWorkOrderProcedureByWorkCenterNewMethod(DefaultCraftPushDownDTO craftPushDownDTO) {
        if (Objects.isNull(craftPushDownDTO.getCraftId())) {
            return new WorkOrderProcedureDTO(craftPushDownDTO);
        }
        // 获取该物料工作中心不为空的工艺工序
        List<CraftProcedureEntity> workCenterNotEmptyCraftProcedureList = listWorkCenterNotEmptyCraftProcedure(craftPushDownDTO);
        if (CollectionUtils.isEmpty(workCenterNotEmptyCraftProcedureList)) {
            return new WorkOrderProcedureDTO(craftPushDownDTO);
        }
        // 初始化需要创建工单的工序
        List<CraftProcedureEntity> createWorkOrderCraftProcedures = new ArrayList<>();
        // 创建的工单需要绑定的工序
        Map<Integer, List<CraftProcedureEntity>> idRelationCraftProceduresMap = new HashMap<>(8);
        // 将工作中心类型相同的数据归属一个工单，不连续也要归属为一个工单
        Map<String, List<CraftProcedureEntity>> collect = workCenterNotEmptyCraftProcedureList.stream().collect(Collectors.groupingBy(CraftProcedureEntity::getWorkCenterIds, LinkedHashMap::new, Collectors.toList()));
        for (Map.Entry<String, List<CraftProcedureEntity>> entry : collect.entrySet()) {
            List<CraftProcedureEntity> craftProcedureEntities = entry.getValue();
            CraftProcedureEntity craftProcedureEntity = craftProcedureEntities.get(0);
            String craftProcedureIds = craftProcedureEntities.stream().map(CraftProcedureEntity::getId).map(String::valueOf).collect(Collectors.joining(Constant.CROSSBAR));
            craftProcedureEntity.setCraftProcedureIds(craftProcedureIds);
            createWorkOrderCraftProcedures.add(craftProcedureEntity);
            idRelationCraftProceduresMap.put(craftProcedureEntity.getId(), craftProcedureEntities);
        }
        return new WorkOrderProcedureDTO(craftPushDownDTO, createWorkOrderCraftProcedures, idRelationCraftProceduresMap);
    }

    /**
     * 获取工单工序列表
     */
    private WorkOrderProcedureDTO getWorkOrderProcedureByWorkCenter(DefaultCraftPushDownDTO craftPushDownDTO) {
        if (Objects.isNull(craftPushDownDTO.getCraftId())) {
            return new WorkOrderProcedureDTO(craftPushDownDTO);
        }
        // 获取该物料工作中心不为空的工艺工序
        List<CraftProcedureEntity> workCenterNotEmptyCraftProcedureList = listWorkCenterNotEmptyCraftProcedure(craftPushDownDTO);
        if (CollectionUtils.isEmpty(workCenterNotEmptyCraftProcedureList)) {
            return new WorkOrderProcedureDTO(craftPushDownDTO);
        }
        // 工序连续且工作中心相同, 拆分为同一个生产工单. 注意：分支节点工序和合并节点工序拆分拆分为一个生产工单
        List<Integer> haveBranchesNodes = new ArrayList<>(), haveMergeNodes = new ArrayList<>();
        // 获取有分支节点、有合并的节点。 可能之间有重复的,一个工序节点既可以是分支节点也可以是合并
        for (CraftProcedureEntity procedure : workCenterNotEmptyCraftProcedureList) {
            String[] supIds = StringUtils.isEmpty(procedure.getSupProcedureId()) ? null : procedure.getSupProcedureId().split(Constant.SEP);
            String[] nextIds = StringUtils.isEmpty(procedure.getNextProcedureId()) ? null : procedure.getNextProcedureId().split(Constant.SEP);
            if (Objects.nonNull(supIds) && supIds.length > 1) {
                haveMergeNodes.add(procedure.getId());
            }
            if (Objects.nonNull(nextIds) && nextIds.length > 1) {
                haveBranchesNodes.add(procedure.getId());
            }
        }
        // 先将有分支或有合并的节点,拆分为同一个生产工单
        Set<Integer> branchesAndMergeNodeSet = new HashSet<>();
        branchesAndMergeNodeSet.addAll(haveBranchesNodes);
        branchesAndMergeNodeSet.addAll(haveMergeNodes);
        Map<Integer, CraftProcedureEntity> idCraftProcedureMap = workCenterNotEmptyCraftProcedureList.stream().collect(Collectors.toMap(CraftProcedureEntity::getId, v -> v));
        // 按工艺路线下推：连续且工作中心相同的，拆分为一个生产工单。
        // 初始化需要创建工单的工序
        List<CraftProcedureEntity> createWorkOrderCraftProcedures = new ArrayList<>();
        // 创建的工单需要绑定的工序
        Map<Integer, List<CraftProcedureEntity>> idRelationCraftProceduresMap = new HashMap<>(8);
        // 索引代表节点，记录当前工序节点是否已经拆分为生产工单
        Map<Integer, Boolean> marked = new HashMap<>(workCenterNotEmptyCraftProcedureList.size());
        for (Integer id : branchesAndMergeNodeSet) {
            CraftProcedureEntity newEntity = idCraftProcedureMap.get(id);
            newEntity.setCraftProcedureIds(String.valueOf(newEntity.getId()));
            idRelationCraftProceduresMap.put(newEntity.getId(), Stream.of(newEntity).collect(Collectors.toList()));
            createWorkOrderCraftProcedures.add(newEntity);
            // 标记工序已经拆分为生产工单了
            marked.put(id, true);
        }
        // 默认原逻辑改成按工作中心拆分
        //没有分支或合并工序独立拆分走原逻辑
        if (Objects.isNull(craftPushDownDTO.getWorkOrderSplittingRules()) || WorkOrderSplittingRulesTypeEnum.WORK_CENTER.getTypeCode().equals(craftPushDownDTO.getWorkOrderSplittingRules()) || CollectionUtils.isEmpty(branchesAndMergeNodeSet)) {
            return getContinuousWorkCenter(workCenterNotEmptyCraftProcedureList, branchesAndMergeNodeSet, craftPushDownDTO);
        }
        if (WorkOrderSplittingRulesTypeEnum.PROCEDURE.getTypeCode().equals(craftPushDownDTO.getWorkOrderSplittingRules())) {
            return getIndependentSplittingMergingProcesses(workCenterNotEmptyCraftProcedureList, branchesAndMergeNodeSet, marked, idCraftProcedureMap, createWorkOrderCraftProcedures, idRelationCraftProceduresMap, craftPushDownDTO);
        }
        //选择按工作中拆分
//        if (WorkOrderSplittingRulesTypeEnum.WORK_CENTER.getTypeCode().equals(craftPushDownDTO.getWorkOrderSplittingRules())) {
//            return getContinuousWorkCenter(workCenterNotEmptyCraftProcedureList, branchesAndMergeNodeSet, craftPushDownDTO);
//        }
        //并行工序单独拆分（合并工序前置）
        if (WorkOrderSplittingRulesTypeEnum.PRE_MERGE_OPERATION.getTypeCode().equals(craftPushDownDTO.getWorkOrderSplittingRules())) {
            return getPreMergeOperation(workCenterNotEmptyCraftProcedureList, branchesAndMergeNodeSet, marked, idCraftProcedureMap, createWorkOrderCraftProcedures, idRelationCraftProceduresMap, craftPushDownDTO);
        }
        //并行工序单独拆分（合并工序后置）
        if (WorkOrderSplittingRulesTypeEnum.POST_MERGE_OPERATION.getTypeCode().equals(craftPushDownDTO.getWorkOrderSplittingRules())) {
            return getPostMergeOperation(workCenterNotEmptyCraftProcedureList, branchesAndMergeNodeSet, marked, idCraftProcedureMap, createWorkOrderCraftProcedures, idRelationCraftProceduresMap, craftPushDownDTO);
        }
        return new WorkOrderProcedureDTO(craftPushDownDTO);
    }


    /**
     * 将合并或者拆分节点先单独拎出，然后将该节点加入到该节点的后置工序且工作中心相同的组中，如果该节点存在多个相同工作中心的后置则先遍历哪个就将该节点合并给谁：
     * 例子：a、b、c都是同一个工作中心，a、b是c的分叉，如何合并，合并给谁？（谁先拆分出去合并给谁）
     *
     * @param workCenterNotEmptyCraftProcedureList
     * @param branchesAndMergeNodeSet              并行工序单独拆分（合并工序后置）
     * @param marked
     * @param idCraftProcedureMap
     * @param createWorkOrderCraftProcedures
     * @param idRelationCraftProceduresMap
     * @return
     */
    private WorkOrderProcedureDTO getPostMergeOperation(List<CraftProcedureEntity> workCenterNotEmptyCraftProcedureList, Set<Integer> branchesAndMergeNodeSet, Map<Integer, Boolean> marked, Map<Integer, CraftProcedureEntity> idCraftProcedureMap, List<CraftProcedureEntity> createWorkOrderCraftProcedures, Map<Integer, List<CraftProcedureEntity>> idRelationCraftProceduresMap, DefaultCraftPushDownDTO craftPushDownDTO) {
        // 然后在处理剩余的节点
        //标记合并或者分支节点已被使用
        Set<Integer> isUser = new HashSet<>();
        for (CraftProcedureEntity procedure : workCenterNotEmptyCraftProcedureList) {
            Integer id = procedure.getId();
            boolean isSplit = marked.getOrDefault(id, false);
            if (isSplit || branchesAndMergeNodeSet.contains(id)) {
                continue;
            }
            // 对不是分支节点工序又不是合并节点工序 进行处理
            CraftProcedureEntity newEntity = idCraftProcedureMap.get(id);
            String newWorkCenterIds = newEntity.getWorkCenterIds();
            String[] supIds = StringUtils.isEmpty(procedure.getSupProcedureId()) ? null : procedure.getSupProcedureId().split(Constant.SEP);
            String[] nextIds = StringUtils.isEmpty(procedure.getNextProcedureId()) ? null : procedure.getNextProcedureId().split(Constant.SEP);
            Integer tempSupId = Objects.isNull(supIds) ? null : Integer.valueOf(supIds[0]);
            Integer tempNextId = Objects.isNull(nextIds) ? null : Integer.valueOf(nextIds[0]);

            List<CraftProcedureEntity> sameWorkCenterCraftProcedures = new ArrayList<>();
            // 加上本身工序
            sameWorkCenterCraftProcedures.add(newEntity);

            // 顺着当前工序往上找相同工作中心的工序
            while (tempSupId != null) {
                CraftProcedureEntity supEntity = idCraftProcedureMap.get(tempSupId);
                // 是否已经被创建
                boolean isSplitTemp = marked.getOrDefault(tempSupId, false);
                if (isSplitTemp && branchesAndMergeNodeSet.contains(tempSupId) && newWorkCenterIds.equals(supEntity.getWorkCenterIds()) && !isUser.contains(tempSupId)) {
                    // 工序连续且工作中心相同
                    sameWorkCenterCraftProcedures.add(supEntity);
                    isUser.add(tempSupId);
                    createWorkOrderCraftProcedures.remove(supEntity);
                    break;
                }
                if (isSplitTemp || Objects.isNull(supEntity) || !newWorkCenterIds.equals(supEntity.getWorkCenterIds())) {
                    break;
                }
                // 工序连续且工作中心相同
                sameWorkCenterCraftProcedures.add(supEntity);

                String[] supIdsTemp = StringUtils.isEmpty(supEntity.getSupProcedureId()) ? null : supEntity.getSupProcedureId().split(Constant.SEP);
                tempSupId = Objects.isNull(supIdsTemp) ? null : Integer.valueOf(supIdsTemp[0]);
            }
            // 顺着当前工序往下找相同工作中心的工序
            while (tempNextId != null) {
                CraftProcedureEntity nextEntity = idCraftProcedureMap.get(tempNextId);
                // 是否已经被创建
                boolean isSplitTemp = marked.getOrDefault(tempNextId, false);
                if (isSplitTemp || Objects.isNull(nextEntity) || !newWorkCenterIds.equals(nextEntity.getWorkCenterIds())) {
                    break;
                }
                // 工序连续且工作中心相同
                sameWorkCenterCraftProcedures.add(nextEntity);

                String[] nextIdsTemp = StringUtils.isEmpty(nextEntity.getNextProcedureId()) ? null : nextEntity.getNextProcedureId().split(Constant.SEP);
                tempNextId = Objects.isNull(nextIdsTemp) ? null : Integer.valueOf(nextIdsTemp[0]);
            }
            // 插入工艺工序，用于下推时工单绑定工序
            idRelationCraftProceduresMap.put(newEntity.getId(), sameWorkCenterCraftProcedures);
            String craftProcedureIds = sameWorkCenterCraftProcedures.stream().map(CraftProcedureEntity::getId).map(String::valueOf).collect(Collectors.joining(com.yelink.dfscommon.constant.Constant.CROSSBAR));
            newEntity.setCraftProcedureIds(craftProcedureIds);
            createWorkOrderCraftProcedures.add(newEntity);
            // 标记工序已经拆分为生产工单了
            sameWorkCenterCraftProcedures.forEach(res -> marked.put(res.getId(), true));
        }
        return new WorkOrderProcedureDTO(craftPushDownDTO, createWorkOrderCraftProcedures, idRelationCraftProceduresMap);
    }

    /**
     * 1、如果工序的工作中心都是一样的则不需要拆分工单
     * 2、如果存在多个不同的工作中心，则遍历工序，将所有连续的且工作中心相同的工序拆分为一组
     *
     * @param workCenterNotEmptyCraftProcedureList
     * @param branchesAndMergeNodeSet
     * @return
     */
    private WorkOrderProcedureDTO getContinuousWorkCenter(List<CraftProcedureEntity> workCenterNotEmptyCraftProcedureList, Set<Integer> branchesAndMergeNodeSet, DefaultCraftPushDownDTO craftPushDownDTO) {
        // 初始化需要创建工单的工序
        List<CraftProcedureEntity> createWorkOrderCraftProcedures = new ArrayList<>();
        // 按工艺路线下推：连续且工作中心相同的，拆分为一个生产工单。
        // 创建的工单需要绑定的工序
        Map<Integer, List<CraftProcedureEntity>> idRelationCraftProceduresMap = new HashMap<>(8);
        Map<Integer, CraftProcedureEntity> idCraftProcedureMap = workCenterNotEmptyCraftProcedureList.stream().collect(Collectors.toMap(CraftProcedureEntity::getId, v -> v));
        //都为一个工作中心不拆
        Map<String, List<CraftProcedureEntity>> collect = workCenterNotEmptyCraftProcedureList.stream().collect(Collectors.groupingBy(CraftProcedureEntity::getWorkCenterIds));
        if (collect.size() == 1) {
            idRelationCraftProceduresMap.put(workCenterNotEmptyCraftProcedureList.get(0).getId(), workCenterNotEmptyCraftProcedureList);
            String craftProcedureIds = workCenterNotEmptyCraftProcedureList.stream().map(CraftProcedureEntity::getId).map(String::valueOf).collect(Collectors.joining(com.yelink.dfscommon.constant.Constant.CROSSBAR));
            workCenterNotEmptyCraftProcedureList.get(0).setCraftProcedureIds(craftProcedureIds);
            createWorkOrderCraftProcedures.add(workCenterNotEmptyCraftProcedureList.get(0));
            return new WorkOrderProcedureDTO(craftPushDownDTO, createWorkOrderCraftProcedures, idRelationCraftProceduresMap);
        }
        // 索引代表节点，记录当前工序节点是否已经拆分为生产工单
        Map<Integer, Boolean> marked = new HashMap<>(workCenterNotEmptyCraftProcedureList.size());
        // 遍历工序处理数据
        for (CraftProcedureEntity procedure : workCenterNotEmptyCraftProcedureList) {
            Integer id = procedure.getId();
            boolean isSplit = marked.getOrDefault(id, false);
            //已处理，直接跳过
            if (isSplit) {
                continue;
            }
            CraftProcedureEntity newEntity = idCraftProcedureMap.get(id);
            String newWorkCenterIds = newEntity.getWorkCenterIds();
            //前置工序
            List<String> supIds = StringUtils.isEmpty(procedure.getSupProcedureId()) ? null : Arrays.asList(procedure.getSupProcedureId().split(Constant.SEP));
            //后置工序
            List<String> nextIds = StringUtils.isEmpty(procedure.getNextProcedureId()) ? null : Arrays.asList(procedure.getNextProcedureId().split(Constant.SEP));
            List<CraftProcedureEntity> sameWorkCenterCraftProcedures = new ArrayList<>();
            // 加上本身工序
            sameWorkCenterCraftProcedures.add(newEntity);

            //处理前置工序找相同工作中心的工序
            handleSups(newWorkCenterIds, supIds, branchesAndMergeNodeSet, marked, idCraftProcedureMap, sameWorkCenterCraftProcedures);
            //处理后置工序找相同工作中心的工序
            handleNexts(newWorkCenterIds, nextIds, branchesAndMergeNodeSet, marked, idCraftProcedureMap, sameWorkCenterCraftProcedures);
            // 插入工艺工序，用于下推时工单绑定工序
            idRelationCraftProceduresMap.put(newEntity.getId(), sameWorkCenterCraftProcedures);
            String craftProcedureIds = sameWorkCenterCraftProcedures.stream().map(CraftProcedureEntity::getId).map(String::valueOf).collect(Collectors.joining(com.yelink.dfscommon.constant.Constant.CROSSBAR));
            newEntity.setCraftProcedureIds(craftProcedureIds);
            createWorkOrderCraftProcedures.add(newEntity);
            // 标记工序已经拆分为生产工单了
            sameWorkCenterCraftProcedures.forEach(res -> marked.put(res.getId(), true));
        }
        return new WorkOrderProcedureDTO(craftPushDownDTO, createWorkOrderCraftProcedures, idRelationCraftProceduresMap);
    }

    private void handleSups(String newWorkCenterIds, List<String> supIds, Set<Integer> branchesAndMergeNodeSet, Map<Integer, Boolean> marked, Map<Integer, CraftProcedureEntity> idCraftProcedureMap, List<CraftProcedureEntity> sameWorkCenterCraftProcedures) {
        if (CollectionUtils.isEmpty(supIds)) {
            return;
        }
        for (int i = 0; i < supIds.size(); i++) {
            Integer tempSupId = Integer.valueOf(supIds.get(i));
            CraftProcedureEntity supEntity = idCraftProcedureMap.get(tempSupId);
            // 是否已经被创建
            boolean isSplitTemp = marked.getOrDefault(tempSupId, false);
            if (isSplitTemp || Objects.isNull(supEntity) || !newWorkCenterIds.equals(supEntity.getWorkCenterIds()) || sameWorkCenterCraftProcedures.contains(supEntity)) {
                continue;
            }
            // 工序连续且工作中心相同
            sameWorkCenterCraftProcedures.add(supEntity);
            List<String> supIdsTemps = StringUtils.isEmpty(supEntity.getSupProcedureId()) ? null : Arrays.asList(supEntity.getSupProcedureId().split(Constant.SEP));
            List<String> nextIdTemps = StringUtils.isEmpty(supEntity.getNextProcedureId()) ? null : Arrays.asList(supEntity.getNextProcedureId().split(Constant.SEP));
            handleSups(newWorkCenterIds, supIdsTemps, branchesAndMergeNodeSet, marked, idCraftProcedureMap, sameWorkCenterCraftProcedures);
            if (branchesAndMergeNodeSet.contains(tempSupId)) {
                handleNexts(newWorkCenterIds, nextIdTemps, branchesAndMergeNodeSet, marked, idCraftProcedureMap, sameWorkCenterCraftProcedures);
            }
        }
    }


    private void handleNexts(String newWorkCenterIds, List<String> nextIds, Set<Integer> branchesAndMergeNodeSet, Map<Integer, Boolean> marked, Map<Integer, CraftProcedureEntity> idCraftProcedureMap, List<CraftProcedureEntity> sameWorkCenterCraftProcedures) {
        if (CollectionUtils.isEmpty(nextIds)) {
            return;
        }
        for (int i = 0; i < nextIds.size(); i++) {
            Integer tempSupId = Integer.valueOf(nextIds.get(i));
            CraftProcedureEntity supEntity = idCraftProcedureMap.get(tempSupId);
            // 是否已经被创建
            boolean isSplitTemp = marked.getOrDefault(tempSupId, false);
            if (isSplitTemp || Objects.isNull(supEntity) || !newWorkCenterIds.equals(supEntity.getWorkCenterIds()) || sameWorkCenterCraftProcedures.contains(supEntity)) {
                continue;
            }
            // 工序连续且工作中心相同
            sameWorkCenterCraftProcedures.add(supEntity);
            List<String> nextIdTemps = StringUtils.isEmpty(supEntity.getNextProcedureId()) ? null : Arrays.asList(supEntity.getNextProcedureId().split(Constant.SEP));
            List<String> supIdsTemps = StringUtils.isEmpty(supEntity.getSupProcedureId()) ? null : Arrays.asList(supEntity.getSupProcedureId().split(Constant.SEP));
            handleNexts(newWorkCenterIds, nextIdTemps, branchesAndMergeNodeSet, marked, idCraftProcedureMap, sameWorkCenterCraftProcedures);
            if (branchesAndMergeNodeSet.contains(tempSupId)) {
                handleSups(newWorkCenterIds, supIdsTemps, branchesAndMergeNodeSet, marked, idCraftProcedureMap, sameWorkCenterCraftProcedures);
            }
        }
    }

    /**
     * 将合并或者拆分节点先单独拎出，然后将该节点加入到该节点的前序工序且工作中心相同的组中，如果该节点存在多个相同工作中心的前置工序则先遍历哪个就将该节点合并给谁：
     * 例子：a、b、c都是同一个工作中心，c是a、b的合并，如何合并，合并给谁？（谁先拆分出去合并给谁）
     *
     * @param workCenterNotEmptyCraftProcedureList
     * @param branchesAndMergeNodeSet
     * @param marked
     * @param idCraftProcedureMap
     * @param createWorkOrderCraftProcedures
     * @param idRelationCraftProceduresMap
     * @return
     */
    private WorkOrderProcedureDTO getPreMergeOperation(List<CraftProcedureEntity> workCenterNotEmptyCraftProcedureList, Set<Integer> branchesAndMergeNodeSet, Map<Integer, Boolean> marked, Map<Integer, CraftProcedureEntity> idCraftProcedureMap, List<CraftProcedureEntity> createWorkOrderCraftProcedures, Map<Integer, List<CraftProcedureEntity>> idRelationCraftProceduresMap, DefaultCraftPushDownDTO craftPushDownDTO) {
        // 然后在处理剩余的节点
        //标记合并或者分支节点已被使用
        Set<Integer> isUser = new HashSet<>();
        for (CraftProcedureEntity procedure : workCenterNotEmptyCraftProcedureList) {
            Integer id = procedure.getId();
            boolean isSplit = marked.getOrDefault(id, false);
            if (isSplit || branchesAndMergeNodeSet.contains(id)) {
                continue;
            }
            // 对不是分支节点工序又不是合并节点工序 进行处理
            CraftProcedureEntity newEntity = idCraftProcedureMap.get(id);
            String newWorkCenterIds = newEntity.getWorkCenterIds();
            //前置工序
            String[] supIds = StringUtils.isEmpty(procedure.getSupProcedureId()) ? null : procedure.getSupProcedureId().split(Constant.SEP);
            //后置工序
            String[] nextIds = StringUtils.isEmpty(procedure.getNextProcedureId()) ? null : procedure.getNextProcedureId().split(Constant.SEP);
            Integer tempSupId = Objects.isNull(supIds) ? null : Integer.valueOf(supIds[0]);
            Integer tempNextId = Objects.isNull(nextIds) ? null : Integer.valueOf(nextIds[0]);

            List<CraftProcedureEntity> sameWorkCenterCraftProcedures = new ArrayList<>();
            // 加上本身工序
            sameWorkCenterCraftProcedures.add(newEntity);

            // 顺着当前工序往上找相同工作中心的工序
            while (tempSupId != null) {
                CraftProcedureEntity supEntity = idCraftProcedureMap.get(tempSupId);
                // 是否已经被创建
                boolean isSplitTemp = marked.getOrDefault(tempSupId, false);
                if (isSplitTemp || Objects.isNull(supEntity) || !newWorkCenterIds.equals(supEntity.getWorkCenterIds()) && !isUser.contains(tempNextId)) {
                    break;
                }
                // 工序连续且工作中心相同
                sameWorkCenterCraftProcedures.add(supEntity);

                String[] supIdsTemp = StringUtils.isEmpty(supEntity.getSupProcedureId()) ? null : supEntity.getSupProcedureId().split(Constant.SEP);
                tempSupId = Objects.isNull(supIdsTemp) ? null : Integer.valueOf(supIdsTemp[0]);
            }
            // 顺着当前工序往下找相同工作中心的工序
            while (tempNextId != null) {
                CraftProcedureEntity nextEntity = idCraftProcedureMap.get(tempNextId);
                // 是否已经被创建
                boolean isSplitTemp = marked.getOrDefault(tempNextId, false);
                if (isSplitTemp && branchesAndMergeNodeSet.contains(tempNextId) && newWorkCenterIds.equals(nextEntity.getWorkCenterIds()) && !isUser.contains(tempNextId)) {
                    // 工序连续且工作中心相同
                    sameWorkCenterCraftProcedures.add(nextEntity);
                    isUser.add(tempNextId);
                    createWorkOrderCraftProcedures.remove(nextEntity);
                    break;
                }
                if (isSplitTemp || Objects.isNull(nextEntity) || !newWorkCenterIds.equals(nextEntity.getWorkCenterIds())) {
                    break;
                }
                // 工序连续且工作中心相同
                sameWorkCenterCraftProcedures.add(nextEntity);
                String[] nextIdsTemp = StringUtils.isEmpty(nextEntity.getNextProcedureId()) ? null : nextEntity.getNextProcedureId().split(Constant.SEP);
                tempNextId = Objects.isNull(nextIdsTemp) ? null : Integer.valueOf(nextIdsTemp[0]);
            }
            // 插入工艺工序，用于下推时工单绑定工序
            idRelationCraftProceduresMap.put(newEntity.getId(), sameWorkCenterCraftProcedures);
            String craftProcedureIds = sameWorkCenterCraftProcedures.stream().map(CraftProcedureEntity::getId).map(String::valueOf).collect(Collectors.joining(com.yelink.dfscommon.constant.Constant.CROSSBAR));
            newEntity.setCraftProcedureIds(craftProcedureIds);
            createWorkOrderCraftProcedures.add(newEntity);
            // 标记工序已经拆分为生产工单了
            sameWorkCenterCraftProcedures.forEach(res -> marked.put(res.getId(), true));
        }
        return new WorkOrderProcedureDTO(craftPushDownDTO, createWorkOrderCraftProcedures, idRelationCraftProceduresMap);
    }

    /**
     * 1、先找出工序的直接前序或直接后序有多个工序的工单，将该工序单独提出，下推为一个工单，并标记该工序已经拆分成工单
     * 2、处理剩余的工序：对于没有标记的工单，遍历他的前序和后序，找到所有工作中心相同的工序，将这些工序拼接并标记，下推为一个工单
     * 3、按照步骤二走，直到全部遍历完成
     *
     * @param workCenterNotEmptyCraftProcedureList
     * @param branchesAndMergeNodeSet
     * @param marked
     * @param idCraftProcedureMap
     * @param createWorkOrderCraftProcedures
     * @param idRelationCraftProceduresMap
     * @return
     */
    private WorkOrderProcedureDTO getIndependentSplittingMergingProcesses(List<CraftProcedureEntity> workCenterNotEmptyCraftProcedureList, Set<Integer> branchesAndMergeNodeSet, Map<Integer, Boolean> marked, Map<Integer, CraftProcedureEntity> idCraftProcedureMap, List<CraftProcedureEntity> createWorkOrderCraftProcedures, Map<Integer, List<CraftProcedureEntity>> idRelationCraftProceduresMap, DefaultCraftPushDownDTO craftPushDownDTO) {
        // 然后在处理剩余的节点
        for (CraftProcedureEntity procedure : workCenterNotEmptyCraftProcedureList) {
            Integer id = procedure.getId();
            boolean isSplit = marked.getOrDefault(id, false);
            if (isSplit || branchesAndMergeNodeSet.contains(id)) {
                continue;
            }
            // 对不是分支节点工序又不是合并节点工序 进行处理
            CraftProcedureEntity newEntity = idCraftProcedureMap.get(id);
            String newWorkCenterIds = newEntity.getWorkCenterIds();
            String[] supIds = StringUtils.isEmpty(procedure.getSupProcedureId()) ? null : procedure.getSupProcedureId().split(Constant.SEP);
            String[] nextIds = StringUtils.isEmpty(procedure.getNextProcedureId()) ? null : procedure.getNextProcedureId().split(Constant.SEP);
            Integer tempSupId = Objects.isNull(supIds) ? null : Integer.valueOf(supIds[0]);
            Integer tempNextId = Objects.isNull(nextIds) ? null : Integer.valueOf(nextIds[0]);

            List<CraftProcedureEntity> sameWorkCenterCraftProcedures = new ArrayList<>();
            // 加上本身工序
            sameWorkCenterCraftProcedures.add(newEntity);

            // 顺着当前工序往上找相同工作中心的工序
            while (tempSupId != null) {
                CraftProcedureEntity supEntity = idCraftProcedureMap.get(tempSupId);
                // 是否已经被创建
                boolean isSplitTemp = marked.getOrDefault(tempSupId, false);
                if (isSplitTemp || Objects.isNull(supEntity) || !newWorkCenterIds.equals(supEntity.getWorkCenterIds())) {
                    break;
                }
                // 工序连续且工作中心相同
                sameWorkCenterCraftProcedures.add(supEntity);

                String[] supIdsTemp = StringUtils.isEmpty(supEntity.getSupProcedureId()) ? null : supEntity.getSupProcedureId().split(Constant.SEP);
                tempSupId = Objects.isNull(supIdsTemp) ? null : Integer.valueOf(supIdsTemp[0]);
            }
            // 顺着当前工序往下找相同工作中心的工序
            while (tempNextId != null) {
                CraftProcedureEntity nextEntity = idCraftProcedureMap.get(tempNextId);
                // 是否已经被创建
                boolean isSplitTemp = marked.getOrDefault(tempNextId, false);
                if (isSplitTemp || Objects.isNull(nextEntity) || !newWorkCenterIds.equals(nextEntity.getWorkCenterIds())) {
                    break;
                }
                // 工序连续且工作中心相同
                sameWorkCenterCraftProcedures.add(nextEntity);

                String[] nextIdsTemp = StringUtils.isEmpty(nextEntity.getNextProcedureId()) ? null : nextEntity.getNextProcedureId().split(Constant.SEP);
                tempNextId = Objects.isNull(nextIdsTemp) ? null : Integer.valueOf(nextIdsTemp[0]);
            }
            // 插入工艺工序，用于下推时工单绑定工序
            idRelationCraftProceduresMap.put(newEntity.getId(), sameWorkCenterCraftProcedures);
            String craftProcedureIds = sameWorkCenterCraftProcedures.stream().map(CraftProcedureEntity::getId).map(String::valueOf).collect(Collectors.joining(com.yelink.dfscommon.constant.Constant.CROSSBAR));
            newEntity.setCraftProcedureIds(craftProcedureIds);
            createWorkOrderCraftProcedures.add(newEntity);
            // 标记工序已经拆分为生产工单了
            sameWorkCenterCraftProcedures.forEach(res -> marked.put(res.getId(), true));
        }
        return new WorkOrderProcedureDTO(craftPushDownDTO, createWorkOrderCraftProcedures, idRelationCraftProceduresMap);
    }

    /**
     * 获取物料工作中心不为空的工艺工序
     */
    private List<CraftProcedureEntity> listWorkCenterNotEmptyCraftProcedure(DefaultCraftPushDownDTO craftPushDownDTO) {
        String materialCode = craftPushDownDTO.getMaterialCode();
        Integer craftId = craftPushDownDTO.getCraftId();
        CraftEntity craftEntity = JacksonUtil.convertObject(craftService.getById(craftId), CraftEntity.class);
        // 获取工艺绑定的工艺工序列表
        List<CraftProcedureEntity> craftProcedures = new ArrayList<>();
        // 不为空则为按工序日计划下推
        if (StringUtils.isNotBlank(craftPushDownDTO.getCraftProcedureId())) {
            List<Integer> craftProcedureIds = Arrays.stream(craftPushDownDTO.getCraftProcedureId().split(Constants.DIAGONAL_LINE)).map(Integer::valueOf).collect(Collectors.toList());
            List<CraftProcedureEntity> craftProcedureEntities = craftProcedureService.listByIds(craftProcedureIds);
            craftProcedures = JacksonUtil.convertArray(craftProcedureEntities, CraftProcedureEntity.class);
        } else {
            List<CraftProcedureEntity> craftProcedureEntities = craftProcedureService.lambdaQuery().eq(CraftProcedureEntity::getCraftId, craftId).orderByAsc(CraftProcedureEntity::getId).list();
            craftProcedureService.showName(craftProcedureEntities);
            craftProcedures = JacksonUtil.convertArray(craftProcedureEntities, CraftProcedureEntity.class);
        }
        // 按工艺工序路线图的先后顺序排序
        List<CraftProcedureEntity> sortCraftProcedures = CraftProcedureEntity.sortNearProcedures(craftProcedures);
        // 过滤工作中心为空和完全委外的工序
        List<CraftProcedureEntity> filterList = sortCraftProcedures.stream()
                .filter(res -> StringUtils.isNotBlank(res.getWorkCenterIds()) &&
                        !res.getIsSubContractingOperation().equals(OutsourcingProcedureEnum.YES.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterList)) {
            throw new ResponseException(RespCodeEnum.NOT_PUSH_DOWN_BECAUSE_NO_CRAFT.fmtDes(materialCode));
        }
        // 设置工艺编码,后续生产工单时使用
        for (CraftProcedureEntity craftProcedure : filterList) {
            craftProcedure.setCraftCode(craftEntity.getCraftCode());
            craftProcedure.setMaterialCode(materialCode);
        }
        return filterList;
    }

    /**
     * 记录失败的日志
     */
    private void recordLog(String username, String logMsg) {
        String nickName = userService.getNicknameByUsername(username);
        OperationLogEntity productOrderLogEntity = OperationLogEntity.builder()
                .module("计划调度")
                .type(OperationType.ADD)
                .des(logMsg)
                .username(StringUtils.isNotBlank(username) ? username : "admin")
                .nickname(StringUtils.isNotBlank(nickName) ? nickName : "管理员")
                .createTime(new Date())
                .build();
        operationLogService.save(productOrderLogEntity);
    }

    /**
     * 获取默认工艺
     */
    private CraftEntity getDefaultCraft(String username, String materialCode) {
        List<CraftEntity> defaultCraftList = JacksonUtil.convertArray(craftService.getDefaultCraftListByMaterialCode(materialCode), CraftEntity.class);
        // 多个默认工艺不允许下推
        if (defaultCraftList.size() > 1) {
            // 日志记录
            recordLog(username, RespCodeEnum.NOT_PUSH_DOWN_BECAUSE_HAS_MANY_DEFAULT_CRAFT.fmtDes(materialCode).getMsgDes());
            throw new ResponseException(RespCodeEnum.NOT_PUSH_DOWN_BECAUSE_HAS_MANY_DEFAULT_CRAFT.fmtDes(materialCode));
        }
        return CollectionUtils.isEmpty(defaultCraftList) ? null : defaultCraftList.get(0);
    }

    /**
     * 删除附件并删除绑定关系
     *
     * @param workOrderId
     */
    private void removeFile(Integer workOrderId) {
        List<WorkOrderFileEntity> workOrderFileEntities = workOrderFileService.getEntityByWorkOrderId(workOrderId);
        List<Integer> collect = workOrderFileEntities.stream().map(WorkOrderFileEntity::getId).collect(Collectors.toList());
        workOrderFileService.removeByIds(collect);
        for (WorkOrderFileEntity workOrderFileEntity : workOrderFileEntities) {
            uploadService.markUnusedFile(workOrderFileEntity.getFile());
        }
    }


    /**
     * 删除通过脚本找不到的表
     */
    private void deleteOtherRelatedDataByWorkOrder(String workOrderNumber) {
        // 设备日历表
        deviceCalendarService.lambdaUpdate().eq(DeviceCalendarEntity::getOrderNumber, workOrderNumber).remove();
        // 工单投产顺序表
        orderExecuteSeqService.lambdaUpdate().eq(OrderExecuteSeqEntity::getOrderNumber, workOrderNumber).remove();
        // 流水码表及记录表
        productFlowCodeService.lambdaUpdate().eq(ProductFlowCodeEntity::getRelationNumber, workOrderNumber).remove();
        LambdaQueryWrapper<ProductFlowCodeRecordEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductFlowCodeRecordEntity::getRelationNumber, workOrderNumber);
        productFlowCodeRecordMapper.delete(wrapper);
        // 设备工单相关记录表
        recordDeviceDayUnionService.lambdaUpdate().eq(RecordDeviceDayUnionEntity::getOrderNumber, workOrderNumber).remove();
        recordDeviceOrderUnionService.lambdaUpdate().eq(RecordDeviceOrderUnionEntity::getOrderNumber, workOrderNumber).remove();
        recordDeviceDayRunService.lambdaUpdate().eq(RecordDeviceDayRunEntity::getOrderNumber, workOrderNumber).remove();
        reportDeviceOrderFinishService.lambdaUpdate().eq(ReportDeviceOrderFinishEntity::getOrderNumber, workOrderNumber).remove();
        // 手动采集指标记录表
        LambdaQueryWrapper<RecordManualCollectionEntity> collectWrapper = new LambdaQueryWrapper<>();
        collectWrapper.eq(RecordManualCollectionEntity::getBatch, workOrderNumber);
        recordManualCollectionMapper.delete(collectWrapper);
        // 批次表
        LambdaQueryWrapper<BarCodeEntity> barCodeWrapper = new LambdaQueryWrapper<>();
        barCodeWrapper.eq(BarCodeEntity::getRelateNumber, workOrderNumber).eq(BarCodeEntity::getRuleType, BarCodeTypeEnum.FINISHED.getCode());
        barCodeMapper.delete(barCodeWrapper);
        // 工单用料清单
        List<Integer> workOrderMaterialListIds = workOrderMaterialListService.lambdaQuery().select(WorkOrderMaterialListEntity::getMaterialListId)
                .eq(WorkOrderMaterialListEntity::getRelateType, MaterialListRelateOrderEnum.WORK_ORDER.getCode())
                .eq(WorkOrderMaterialListEntity::getRelateNumber, workOrderNumber)
                .list().stream().map(WorkOrderMaterialListEntity::getMaterialListId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(workOrderMaterialListIds)) {
            workOrderMaterialListMaterialService.lambdaUpdate().in(WorkOrderMaterialListMaterialEntity::getMaterialListId, workOrderMaterialListIds).remove();
        }
        workOrderMaterialListService.lambdaUpdate().eq(WorkOrderMaterialListEntity::getRelateType, MaterialListRelateOrderEnum.WORK_ORDER.getCode())
                .eq(WorkOrderMaterialListEntity::getRelateNumber, workOrderNumber).remove();
        // 工单统计表
        MetricsWorkOrderService metricsWorkOrderService = SpringUtil.getBean(MetricsWorkOrderService.class);
        metricsWorkOrderService.removeTransactional(workOrderNumber);
        //
        MetricsValuationCalService metricsValuationCalService = SpringUtil.getBean(MetricsValuationCalService.class);
        metricsValuationCalService.removeTransactional(workOrderNumber);
        //
        MetricsWorkOrderHourlyService metricsWorkOrderHourlyService = SpringUtil.getBean(MetricsWorkOrderHourlyService.class);
        metricsWorkOrderHourlyService.removeTransactional(workOrderNumber);
        //
        MetricsWorkOrderDailyService metricsWorkOrderDailyService = SpringUtil.getBean(MetricsWorkOrderDailyService.class);
        metricsWorkOrderDailyService.removeTransactional(workOrderNumber);
        // 删除单据下推记录
        pushDownRecordService.deleteRecord(PushDownRecordDeleteDTO.builder()
                .targetOrderType(OrderNumTypeEnum.WORK_ORDER.getTypeCode())
                .targetOrderNumber(workOrderNumber)
                .build());
        // 单据操作记录表
        orderChangeLogService.lambdaUpdate().eq(OrderChangeLogEntity::getOrderNumber, workOrderNumber).remove();
        // 任务中心
        List<Integer> taskIds = taskService.lambdaQuery().select(TaskEntity::getTaskId)
                .eq(TaskEntity::getOrderCategory, OrderCategoryEnum.WORK_ORDER.getCode())
                .eq(TaskEntity::getOrderNumber, workOrderNumber)
                .list().stream().map(TaskEntity::getTaskId)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(taskIds)) {
            taskUserConfigService.lambdaUpdate().in(TaskUserConfigEntity::getTaskId, taskIds).remove();
            taskService.lambdaUpdate().eq(TaskEntity::getOrderCategory, OrderCategoryEnum.WORK_ORDER.getCode())
                    .eq(TaskEntity::getOrderNumber, workOrderNumber).remove();
            taskLogService.lambdaUpdate().in(TaskLogEntity::getTaskId, taskIds).remove();
        }
    }

    /**
     * 删除单据时记录相关单据
     */
    private void recordDeleteRecordByWorkOrder(WorkOrderEntity workOrderEntity, String username) {
        List<DeleteRecordEntity> deleteRecordEntities = new ArrayList<>();
        // 获取dfs版本号
        String dfsVersion;
        try {
            SysService sysService = SpringUtil.getBean(SysService.class);
            dfsVersion = sysService.dfsVersion();
        } catch (Exception e) {
            dfsVersion = "dfs";
        }
        Date deleteDate = new Date();
        workOrderEntity.setDeleteName(username);
        workOrderEntity.setDeleteTime(deleteDate);
        String workOrderNumber = workOrderEntity.getWorkOrderNumber();
        Integer workOrderId = workOrderEntity.getWorkOrderId();
        // 工单表
        deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_work_order").jsonData(JSON.toJSONString(Stream.of(workOrderEntity).collect(Collectors.toList()))).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        List<OrderChangeLogEntity> changeLogEntities = orderChangeLogService.lambdaQuery().eq(OrderChangeLogEntity::getModel, "工单管理").eq(OrderChangeLogEntity::getOrderNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(changeLogEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_order_change_log").jsonData(JSON.toJSONString(changeLogEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        LambdaQueryWrapper<BarCodeEntity> barCodeWrapper = new LambdaQueryWrapper<>();
        barCodeWrapper.eq(BarCodeEntity::getRelateNumber, workOrderNumber).eq(BarCodeEntity::getRuleType, BarCodeTypeEnum.FINISHED.getCode());
        List<BarCodeEntity> barCodeEntities = barCodeMapper.selectList(barCodeWrapper);
        if (CollectionUtils.isNotEmpty(barCodeEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_bar_code").jsonData(JSON.toJSONString(barCodeEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        List<WorkOrderMaterialListEntity> workOrderMaterialListEntities = workOrderMaterialListService.lambdaQuery().eq(WorkOrderMaterialListEntity::getRelateType, MaterialListRelateOrderEnum.WORK_ORDER.getCode())
                .eq(WorkOrderMaterialListEntity::getRelateNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(workOrderMaterialListEntities)) {
            List<Integer> workOrderMaterialListIds = workOrderMaterialListEntities.stream().map(WorkOrderMaterialListEntity::getMaterialListId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(workOrderMaterialListIds)) {
                List<WorkOrderMaterialListMaterialEntity> workOrderMaterialListMaterialEntities = workOrderMaterialListMaterialService.lambdaQuery().in(WorkOrderMaterialListMaterialEntity::getMaterialListId, workOrderMaterialListIds).list();
                deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_work_order_material_list_material").jsonData(JSON.toJSONString(workOrderMaterialListMaterialEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
            }
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_work_order_material_list").jsonData(JSON.toJSONString(workOrderMaterialListEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        List<DeviceCalendarEntity> deviceCalendarEntities = deviceCalendarService.lambdaQuery().eq(DeviceCalendarEntity::getOrderNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(deviceCalendarEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_device_calendar").jsonData(JSON.toJSONString(deviceCalendarEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        List<OrderExecuteSeqEntity> executeSeqEntities = orderExecuteSeqService.lambdaQuery().eq(OrderExecuteSeqEntity::getOrderNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(executeSeqEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_order_execute_seq").jsonData(JSON.toJSONString(executeSeqEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        List<WorkOrderMaterialCheckMaterialEntity> materialCheckMaterialEntities = workOrderMaterialCheckMaterialService.lambdaQuery().eq(WorkOrderMaterialCheckMaterialEntity::getWorkOrderNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(materialCheckMaterialEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_work_order_material_check_material").jsonData(JSON.toJSONString(materialCheckMaterialEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        List<ProductFlowCodeEntity> productFlowCodeEntities = productFlowCodeService.lambdaQuery().eq(ProductFlowCodeEntity::getRelationNumber, workOrderNumber).eq(ProductFlowCodeEntity::getType, ProductFlowCodeTypeEnum.PRODUCT_FLOW_CODE.getCode()).list();
        if (CollectionUtils.isNotEmpty(productFlowCodeEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_product_flow_code").jsonData(JSON.toJSONString(productFlowCodeEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        LambdaQueryWrapper<ProductFlowCodeRecordEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductFlowCodeRecordEntity::getRelationNumber, workOrderNumber).eq(ProductFlowCodeRecordEntity::getType, ProductFlowCodeTypeEnum.PRODUCT_FLOW_CODE.getCode());
        List<ProductFlowCodeRecordEntity> productFlowCodeRecordEntities = productFlowCodeRecordMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(productFlowCodeRecordEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_product_flow_code_record").jsonData(JSON.toJSONString(productFlowCodeRecordEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        List<RecordDeviceDayUnionEntity> deviceDayUnionEntities = recordDeviceDayUnionService.lambdaQuery().eq(RecordDeviceDayUnionEntity::getOrderNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(deviceDayUnionEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_record_device_day_union").jsonData(JSON.toJSONString(deviceDayUnionEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        List<RecordDeviceOrderUnionEntity> deviceOrderUnionEntities = recordDeviceOrderUnionService.lambdaQuery().eq(RecordDeviceOrderUnionEntity::getOrderNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(deviceOrderUnionEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_record_device_order_union").jsonData(JSON.toJSONString(deviceOrderUnionEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        List<RecordDeviceDayRunEntity> deviceDayRunEntities = recordDeviceDayRunService.lambdaQuery().eq(RecordDeviceDayRunEntity::getOrderNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(deviceDayRunEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_record_device_day_run").jsonData(JSON.toJSONString(deviceDayRunEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        List<ReportDeviceOrderFinishEntity> deviceOrderFinishEntities = reportDeviceOrderFinishService.lambdaQuery().eq(ReportDeviceOrderFinishEntity::getOrderNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(deviceOrderFinishEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_report_device_order_finish").jsonData(JSON.toJSONString(deviceOrderFinishEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        LambdaQueryWrapper<RecordManualCollectionEntity> collectWrapper = new LambdaQueryWrapper<>();
        collectWrapper.eq(RecordManualCollectionEntity::getBatch, workOrderNumber);
        List<RecordManualCollectionEntity> manualCollectionEntities = recordManualCollectionMapper.selectList(collectWrapper);
        if (CollectionUtils.isNotEmpty(manualCollectionEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_record_manual_collection").jsonData(JSON.toJSONString(manualCollectionEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        AlarmService alarmService = SpringUtil.getBean(AlarmService.class);
        List<AlarmEntity> alarmEntities = alarmService.lambdaQuery().eq(AlarmEntity::getWorkOrderNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(alarmEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_alarm").jsonData(JSON.toJSONString(alarmEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        AttendanceService attendanceService = SpringUtil.getBean(AttendanceService.class);
        List<AttendanceEntity> attendanceEntities = attendanceService.lambdaQuery().eq(AttendanceEntity::getWorkOrderNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(attendanceEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_attendance").jsonData(JSON.toJSONString(attendanceEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        DefectRecordService defectRecordService = SpringUtil.getBean(DefectRecordService.class);
        List<DefectRecordEntity> defectRecordEntities = defectRecordService.lambdaQuery().eq(DefectRecordEntity::getWorkOrder, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(defectRecordEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_defect_record").jsonData(JSON.toJSONString(defectRecordEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        EventRecordService eventRecordService = SpringUtil.getBean(EventRecordService.class);
        List<EventRecordEntity> eventRecordEntities = eventRecordService.lambdaQuery().eq(EventRecordEntity::getWorkOrderNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(eventRecordEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_event_record").jsonData(JSON.toJSONString(eventRecordEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        FacInputRecordService facInputRecordService = SpringUtil.getBean(FacInputRecordService.class);
        List<FacInputRecordEntity> facInputRecordEntities = facInputRecordService.lambdaQuery().eq(FacInputRecordEntity::getWorkOrder, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(facInputRecordEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_fac_input_record").jsonData(JSON.toJSONString(facInputRecordEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        FacUserReportService facUserReportService = SpringUtil.getBean(FacUserReportService.class);
        List<FacUserReportEntity> facUserReportEntities = facUserReportService.lambdaQuery().eq(FacUserReportEntity::getWorkOrder, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(facUserReportEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_fac_user_report").jsonData(JSON.toJSONString(facUserReportEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        FacWorkOrderService facWorkOrderService = SpringUtil.getBean(FacWorkOrderService.class);
        List<FacWorkOrderEntity> facWorkOrderEntities = facWorkOrderService.lambdaQuery().eq(FacWorkOrderEntity::getWorkOrder, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(facWorkOrderEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_fac_work_order").jsonData(JSON.toJSONString(facWorkOrderEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        FeedRecordService feedRecordService = SpringUtil.getBean(FeedRecordService.class);
        List<FeedRecordEntity> feedRecordEntities = feedRecordService.lambdaQuery().eq(FeedRecordEntity::getWorkOrderNum, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(feedRecordEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_feed_record").jsonData(JSON.toJSONString(feedRecordEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        FurnaceService furnaceService = SpringUtil.getBean(FurnaceService.class);
        List<FurnaceEntity> furnaceEntities = furnaceService.lambdaQuery().eq(FurnaceEntity::getWorkOrderNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(furnaceEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_furnace").jsonData(JSON.toJSONString(furnaceEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        InputRecordService inputRecordService = SpringUtil.getBean(InputRecordService.class);
        List<InputRecordEntity> inputRecordEntities = inputRecordService.lambdaQuery().eq(InputRecordEntity::getWorkOrder, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(inputRecordEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_input_record").jsonData(JSON.toJSONString(inputRecordEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        MaintainRecordService maintainRecordService = SpringUtil.getBean(MaintainRecordService.class);
        List<MaintainRecordEntity> maintainRecordEntities = maintainRecordService.lambdaQuery().eq(MaintainRecordEntity::getWorkOrder, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(maintainRecordEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_maintain_record").jsonData(JSON.toJSONString(maintainRecordEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        OrderWorkOrderService orderWorkOrderService = SpringUtil.getBean(OrderWorkOrderService.class);
        List<OrderWorkOrderEntity> orderWorkOrderEntities = orderWorkOrderService.lambdaQuery().eq(OrderWorkOrderEntity::getWorkOrderId, workOrderId).list();
        if (CollectionUtils.isNotEmpty(orderWorkOrderEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_order_work_order_relation").jsonData(JSON.toJSONString(orderWorkOrderEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        PackageBoxService packageBoxService = SpringUtil.getBean(PackageBoxService.class);
        List<PackageBoxEntity> packageBoxEntities = packageBoxService.lambdaQuery().eq(PackageBoxEntity::getWorkOrderNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(packageBoxEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_package_box").jsonData(JSON.toJSONString(packageBoxEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        PackageRecordService packageRecordService = SpringUtil.getBean(PackageRecordService.class);
        List<PackageRecordEntity> packageRecordEntities = packageRecordService.lambdaQuery().eq(PackageRecordEntity::getWorkOrderNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(packageRecordEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_package_record").jsonData(JSON.toJSONString(packageRecordEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        QualityService qualityService = SpringUtil.getBean(QualityService.class);
        List<QualityEntity> qualityEntities = qualityService.lambdaQuery().eq(QualityEntity::getWorkOrder, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(qualityEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_quality").jsonData(JSON.toJSONString(qualityEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        RecordLineInputOutputOrderService recordLineInputOutputOrderService = SpringUtil.getBean(RecordLineInputOutputOrderService.class);
        List<RecordLineInputOutputOrderEntity> lineInputOutputOrderEntities = recordLineInputOutputOrderService.lambdaQuery().eq(RecordLineInputOutputOrderEntity::getWorkOrder, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(lineInputOutputOrderEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_record_line_input_output_order").jsonData(JSON.toJSONString(lineInputOutputOrderEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        RecordLineInputOutputOrderHourService recordLineInputOutputOrderHourService = SpringUtil.getBean(RecordLineInputOutputOrderHourService.class);
        List<RecordLineInputOutputOrderHourEntity> lineInputOutputOrderHourEntities = recordLineInputOutputOrderHourService.lambdaQuery().eq(RecordLineInputOutputOrderHourEntity::getWorkOrder, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(lineInputOutputOrderHourEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_record_line_input_output_order_hour").jsonData(JSON.toJSONString(lineInputOutputOrderHourEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        RecordWorkOrderCountService recordWorkOrderCountService = SpringUtil.getBean(RecordWorkOrderCountService.class);
        List<RecordWorkOrderCountEntity> recordWorkOrderCountEntities = recordWorkOrderCountService.lambdaQuery().eq(RecordWorkOrderCountEntity::getWorkOrderNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(recordWorkOrderCountEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_record_work_order_count").jsonData(JSON.toJSONString(recordWorkOrderCountEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        RecordWorkOrderDayCountService recordWorkOrderDayCountService = SpringUtil.getBean(RecordWorkOrderDayCountService.class);
        List<RecordWorkOrderDayCountEntity> recordWorkOrderDayCountEntities = recordWorkOrderDayCountService.lambdaQuery().eq(RecordWorkOrderDayCountEntity::getWorkOrderNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(recordWorkOrderDayCountEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_record_work_order_day_count").jsonData(JSON.toJSONString(recordWorkOrderDayCountEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        RecordWorkOrderLineDayCountService recordWorkOrderLineDayCountService = SpringUtil.getBean(RecordWorkOrderLineDayCountService.class);
        List<RecordWorkOrderLineDayCountEntity> recordWorkOrderLineDayCountEntities = recordWorkOrderLineDayCountService.lambdaQuery().eq(RecordWorkOrderLineDayCountEntity::getWorkOrderNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(recordWorkOrderLineDayCountEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_record_work_order_line_day_count").jsonData(JSON.toJSONString(recordWorkOrderLineDayCountEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        RecordWorkOrderMaterialPlanService recordWorkOrderMaterialPlanService = SpringUtil.getBean(RecordWorkOrderMaterialPlanService.class);
        List<RecordWorkOrderMaterialPlanEntity> recordWorkOrderMaterialPlanEntities = recordWorkOrderMaterialPlanService.lambdaQuery().eq(RecordWorkOrderMaterialPlanEntity::getWorkOrderNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(recordWorkOrderMaterialPlanEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_record_work_order_material_plan").jsonData(JSON.toJSONString(recordWorkOrderMaterialPlanEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        RecordWorkOrderProgressService recordWorkOrderProgressService = SpringUtil.getBean(RecordWorkOrderProgressService.class);
        List<RecordWorkOrderProgressEntity> recordWorkOrderProgressEntities = recordWorkOrderProgressService.lambdaQuery().eq(RecordWorkOrderProgressEntity::getWorkOrder, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(recordWorkOrderProgressEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_record_work_order_progress").jsonData(JSON.toJSONString(recordWorkOrderProgressEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        RecordWorkOrderStateService recordWorkOrderStateService = SpringUtil.getBean(RecordWorkOrderStateService.class);
        List<RecordWorkOrderStateEntity> recordWorkOrderStateEntities = recordWorkOrderStateService.lambdaQuery().eq(RecordWorkOrderStateEntity::getWorkOrder, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(recordWorkOrderStateEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_record_work_order_state").jsonData(JSON.toJSONString(recordWorkOrderStateEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        MetricsWorkOrderHourlyService metricsWorkOrderHourlyService = SpringUtil.getBean(MetricsWorkOrderHourlyService.class);
        List<MetricsWorkOrderHourlyEntity> metricsWorkOrderHourlyEntities = metricsWorkOrderHourlyService.lambdaQuery().eq(MetricsWorkOrderHourlyEntity::getWorkOrderNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(metricsWorkOrderHourlyEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_metrics_work_order_hourly").jsonData(JSON.toJSONString(metricsWorkOrderHourlyEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        MetricsWorkOrderService metricsWorkOrderService = SpringUtil.getBean(MetricsWorkOrderService.class);
        List<MetricsWorkOrderEntity> metricsWorkOrderEntities = metricsWorkOrderService.lambdaQuery().eq(MetricsWorkOrderEntity::getWorkOrderNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(metricsWorkOrderEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_metrics_work_order").jsonData(JSON.toJSONString(metricsWorkOrderEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        MetricsWorkOrderDailyService metricsWorkOrderDailyService = SpringUtil.getBean(MetricsWorkOrderDailyService.class);
        List<MetricsWorkOrderDailyEntity> metricsWorkOrderDailyEntities = metricsWorkOrderDailyService.lambdaQuery().eq(MetricsWorkOrderDailyEntity::getWorkOrderNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(metricsWorkOrderDailyEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_metrics_work_order_daily").jsonData(JSON.toJSONString(metricsWorkOrderDailyEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        MetricsValuationCalService metricsValuationCalService = SpringUtil.getBean(MetricsValuationCalService.class);
        List<MetricsValuationCalEntity> metricsValuationCalEntities = metricsValuationCalService.lambdaQuery().eq(MetricsValuationCalEntity::getWorkOrderNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(metricsValuationCalEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_metrics_valuation_cal").jsonData(JSON.toJSONString(metricsValuationCalEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        RecordWorkOrderUnqualifiedService recordWorkOrderUnqualifiedService = SpringUtil.getBean(RecordWorkOrderUnqualifiedService.class);
        List<RecordWorkOrderUnqualifiedEntity> recordWorkOrderUnqualifiedEntities = recordWorkOrderUnqualifiedService.lambdaQuery().eq(RecordWorkOrderUnqualifiedEntity::getWorkOrderNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(recordWorkOrderUnqualifiedEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_record_work_order_unqualified").jsonData(JSON.toJSONString(recordWorkOrderUnqualifiedEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        RefiningFurnaceRecordService refiningFurnaceRecordService = SpringUtil.getBean(RefiningFurnaceRecordService.class);
        List<RefiningFurnaceRecordEntity> refiningFurnaceRecordEntities = refiningFurnaceRecordService.lambdaQuery().eq(RefiningFurnaceRecordEntity::getWorkOrderNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(refiningFurnaceRecordEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_refining_furnace_record").jsonData(JSON.toJSONString(refiningFurnaceRecordEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        ReportCountService reportCountService = SpringUtil.getBean(ReportCountService.class);
        List<ReportCountEntity> reportCountEntities = reportCountService.lambdaQuery().eq(ReportCountEntity::getWorkOrder, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(reportCountEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_report_count").jsonData(JSON.toJSONString(reportCountEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        ReportDayCountService reportDayCountService = SpringUtil.getBean(ReportDayCountService.class);
        List<ReportDayCountEntity> reportDayCountEntities = reportDayCountService.lambdaQuery().eq(ReportDayCountEntity::getWorkOrder, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(reportDayCountEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_report_day_count").jsonData(JSON.toJSONString(reportDayCountEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        ReportLineService reportLineService = SpringUtil.getBean(ReportLineService.class);
        List<ReportLineEntity> reportLineEntities = reportLineService.lambdaQuery().eq(ReportLineEntity::getWorkOrder, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(reportLineEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_report_line").jsonData(JSON.toJSONString(reportLineEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        ShiftCountService shiftCountService = SpringUtil.getBean(ShiftCountService.class);
        List<ShiftCountEntity> shiftCountEntities = shiftCountService.lambdaQuery().eq(ShiftCountEntity::getWorkOrderNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(shiftCountEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_shift_count").jsonData(JSON.toJSONString(shiftCountEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        StaffPieceWorkTimeService staffPieceWorkTimeService = SpringUtil.getBean(StaffPieceWorkTimeService.class);
        List<StaffPieceWorkTimeEntity> staffPieceWorkTimeEntities = staffPieceWorkTimeService.lambdaQuery().eq(StaffPieceWorkTimeEntity::getWorkOrderNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(staffPieceWorkTimeEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_staff_piece_work_time").jsonData(JSON.toJSONString(staffPieceWorkTimeEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        TakeOutApplicationService takeOutApplicationService = SpringUtil.getBean(TakeOutApplicationService.class);
        List<TakeOutApplicationEntity> takeOutApplicationEntities = takeOutApplicationService.lambdaQuery().eq(TakeOutApplicationEntity::getWorkOrderNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(takeOutApplicationEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_take_out_application").jsonData(JSON.toJSONString(takeOutApplicationEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        TmpMaterialReadinessInspectionService tmpMaterialReadinessInspectionService = SpringUtil.getBean(TmpMaterialReadinessInspectionService.class);
        List<TmpMaterialReadinessInspectionEntity> tmpMaterialReadinessInspectionEntities = tmpMaterialReadinessInspectionService.lambdaQuery().eq(TmpMaterialReadinessInspectionEntity::getWorkOrderNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(tmpMaterialReadinessInspectionEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_tmp_material_readiness_inspection").jsonData(JSON.toJSONString(tmpMaterialReadinessInspectionEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        WorkOrderBarcodeService workOrderBarcodeService = SpringUtil.getBean(WorkOrderBarcodeService.class);
        List<WorkOrderBarcodeEntity> workOrderBarcodeEntities = workOrderBarcodeService.lambdaQuery().eq(WorkOrderBarcodeEntity::getWorkOrderNum, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(workOrderBarcodeEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_work_order_barcode").jsonData(JSON.toJSONString(workOrderBarcodeEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        List<WorkOrderDeviceRelevanceEntity> workOrderDeviceRelevanceEntities = workOrderDeviceRelevanceService.lambdaQuery().eq(WorkOrderDeviceRelevanceEntity::getWorkOrderId, workOrderId).list();
        if (CollectionUtils.isNotEmpty(workOrderDeviceRelevanceEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_work_order_device_relevance").jsonData(JSON.toJSONString(workOrderDeviceRelevanceEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        List<WorkOrderFileEntity> workOrderFileEntities = workOrderFileService.lambdaQuery().eq(WorkOrderFileEntity::getWorkOrderId, workOrderId).list();
        if (CollectionUtils.isNotEmpty(workOrderFileEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_work_order_file").jsonData(JSON.toJSONString(workOrderFileEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        WorkOrderFlowService workOrderFlowService = SpringUtil.getBean(WorkOrderFlowService.class);
        List<WorkOrderFlowEntity> workOrderFlowEntities = workOrderFlowService.lambdaQuery().eq(WorkOrderFlowEntity::getWorkOrderNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(workOrderFlowEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_work_order_flow").jsonData(JSON.toJSONString(workOrderFlowEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        WorkOrderInputService workOrderInputService = SpringUtil.getBean(WorkOrderInputService.class);
        List<WorkOrderInputEntity> workOrderInputEntities = workOrderInputService.lambdaQuery().eq(WorkOrderInputEntity::getWorkOrderNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(workOrderInputEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_work_order_input").jsonData(JSON.toJSONString(workOrderInputEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        List<WorkOrderLineRelevanceEntity> workOrderLineRelevanceEntities = workOrderLineRelevanceService.lambdaQuery().eq(WorkOrderLineRelevanceEntity::getWorkOrderId, workOrderId).list();
        if (CollectionUtils.isNotEmpty(workOrderLineRelevanceEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_work_order_line_relevance").jsonData(JSON.toJSONString(workOrderLineRelevanceEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        List<WorkOrderPlanEntity> workOrderPlanEntities = workOrderPlanService.lambdaQuery().eq(WorkOrderPlanEntity::getWorkOrderNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(workOrderPlanEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_work_order_plan").jsonData(JSON.toJSONString(workOrderPlanEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        List<WorkOrderProcedureRelationEntity> workOrderProcedureRelationEntities = workOrderProcedureRelationService.lambdaQuery().eq(WorkOrderProcedureRelationEntity::getWorkOrderNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(workOrderProcedureRelationEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_work_order_procedure_relation").jsonData(JSON.toJSONString(workOrderProcedureRelationEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        LambdaQueryWrapper<WorkOrderProductLineRelationEntity> lineRelationEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
        lineRelationEntityLambdaQueryWrapper.eq(WorkOrderProductLineRelationEntity::getWorkOrderId, workOrderId);
        List<WorkOrderProductLineRelationEntity> workOrderProductLineRelationEntities = workOrderProductLineRelationMapper.selectList(lineRelationEntityLambdaQueryWrapper);
        if (CollectionUtils.isNotEmpty(workOrderProductLineRelationEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_work_order_product_line_relation").jsonData(JSON.toJSONString(workOrderProductLineRelationEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        List<WorkOrderTeamEntity> workOrderTeamEntities = workOrderTeamService.lambdaQuery().eq(WorkOrderTeamEntity::getWorkOrderNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(workOrderTeamEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_work_order_team").jsonData(JSON.toJSONString(workOrderTeamEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        List<WorkOrderTeamRelevanceEntity> workOrderTeamRelevanceEntities = workOrderTeamRelevanceService.lambdaQuery().eq(WorkOrderTeamRelevanceEntity::getWorkOrderId, workOrderId).list();
        if (CollectionUtils.isNotEmpty(workOrderTeamRelevanceEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_work_order_team_relevance").jsonData(JSON.toJSONString(workOrderTeamRelevanceEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        // 生产工单投产检查结果明细表
        List<WorkOrderInvestCheckResultEntity> investCheckResultEntities = investCheckResultService.lambdaQuery().eq(WorkOrderInvestCheckResultEntity::getWorkOrderNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(investCheckResultEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_work_order_invest_check_result").jsonData(JSON.toJSONString(investCheckResultEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        // 单据下推记录
        List<OrderPushDownRecordEntity> pushDownRecordEntities = pushDownRecordService.lambdaQuery().eq(OrderPushDownRecordEntity::getTargetOrderNumber, workOrderNumber).eq(OrderPushDownRecordEntity::getTargetOrderType, OrderNumTypeEnum.WORK_ORDER.getTypeCode()).list();
        if (CollectionUtils.isNotEmpty(pushDownRecordEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_order_push_down_record").jsonData(JSON.toJSONString(pushDownRecordEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        // 生产基本单元关联表
        List<WorkOrderBasicUnitRelationEntity> basicUnitRelationEntities = basicUnitRelationService.lambdaQuery().eq(WorkOrderBasicUnitRelationEntity::getWorkOrderNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(basicUnitRelationEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_work_order_basic_unit_relation").jsonData(JSON.toJSONString(basicUnitRelationEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        // 生产基本单元投产记录关联表
        List<WorkOrderBasicUnitInputRecordEntity> basicUnitInputRecordEntities = basicUnitInputRecordService.lambdaQuery().eq(WorkOrderBasicUnitInputRecordEntity::getWorkOrderNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(basicUnitInputRecordEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_work_order_basic_unit_input_record").jsonData(JSON.toJSONString(basicUnitInputRecordEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }

        // 保存删除的记录数据
        Map<String, String> materialCodeNameMap = materialService.getMaterialCodeNameMap(Collections.singleton(workOrderEntity.getMaterialCode()));
        for (DeleteRecordEntity deleteRecord : deleteRecordEntities) {
            deleteRecord.setMaterialName(materialCodeNameMap.get(deleteRecord.getMaterialCode()));
        }
        // 任务中心
        List<TaskEntity> taskEntities = taskService.lambdaQuery().eq(TaskEntity::getOrderCategory, OrderCategoryEnum.WORK_ORDER.getCode()).eq(TaskEntity::getOrderNumber, workOrderNumber).list();
        if (CollectionUtils.isNotEmpty(taskEntities)) {
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_task").jsonData(JSON.toJSONString(taskEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
            List<Integer> taskIds = taskEntities.stream().map(TaskEntity::getTaskId).collect(Collectors.toList());
            List<TaskUserConfigEntity> taskUserConfigEntities = taskUserConfigService.lambdaQuery().in(TaskUserConfigEntity::getTaskId, taskIds).list();
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_task_user_config").jsonData(JSON.toJSONString(taskUserConfigEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
            List<TaskLogEntity> taskLogEntities = taskLogService.lambdaQuery().in(TaskLogEntity::getTaskId, taskIds).list();
            deleteRecordEntities.add(DeleteRecordEntity.builder().orderNumber(workOrderNumber).orderType(ModuleEnum.WORK_ORDER.getCode()).tableName("dfs_task_log").jsonData(JSON.toJSONString(taskLogEntities)).tableSource("dfs").version(dfsVersion).createBy(username).createTime(deleteDate).materialCode(workOrderEntity.getMaterialCode()).build());
        }
        deleteRecordService.saveBatch(deleteRecordEntities);
    }

    /**
     * 删除工单时，删除相关单据
     */
    private void deleteRelatedDataWhenDeleteWorkOrder(WorkOrderEntity workOrderEntity) {
        List<CommonTableDTO> relatedDataTableByWorkOrder = this.baseMapper.getRelatedDataTableByWorkOrder(workPropertise.getTableSchema());
        List<CommonTableDTO> workOrderNumberRelatedTable = relatedDataTableByWorkOrder.stream().filter(o -> o.getColumnName().equals(Constant.WORK_ORDER_NUMBER_FIELD) || o.getColumnName().equals(Constant.WORK_ORDER_FIELD) || o.getColumnName().equals(Constant.WORK_ORDER_NUM_FIELD)).collect(Collectors.toList());
        List<CommonTableDTO> workOrderIdRelatedTable = relatedDataTableByWorkOrder.stream().filter(o -> o.getColumnName().equals(Constant.WORK_ORDER_ID_FIELD)).collect(Collectors.toList());
        for (CommonTableDTO tableDTO : workOrderNumberRelatedTable) {
            this.baseMapper.deleteRelatedDataByWorkOrder(tableDTO.getTableName(), tableDTO.getColumnName(), workOrderEntity.getWorkOrderNumber());
        }
        for (CommonTableDTO tableDTO : workOrderIdRelatedTable) {
            this.baseMapper.deleteRelatedDataByWorkOrder(tableDTO.getTableName(), tableDTO.getColumnName(), workOrderEntity.getWorkOrderId());
        }
    }

    /**
     * 追溯生产工单的生产订单，销售订单
     *
     * @param orderNumber
     * @return
     */
    @Override
    public OrderTraceGroupVO traceOrderByOrderNumber(String orderType, String orderNumber) {
        List<OrderTraceVO> traceVOS = new ArrayList<>();
        if (OrderNumTypeEnum.WORK_ORDER.getTypeCode().equals(orderType)) {
            WorkOrderService workOrderService = SpringUtil.getBean(WorkOrderService.class);
            WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(orderNumber);
            String productOrderNumber = workOrderEntity.getProductOrderNumber();
            String saleOrderNumber = workOrderEntity.getSaleOrderNumber();
            ProductOrderEntity productOrderEntity = null;
            SaleOrderEntity saleOrderEntity = null;
            if (StringUtils.isNotBlank(productOrderNumber)) {
                productOrderEntity = extProductOrderInterface.selectProductOrderByNumber(ProductOrderDetailDTO.builder().productOrderNumber(productOrderNumber).build());
                if (StringUtils.isBlank(saleOrderNumber)) {
                    saleOrderNumber = productOrderEntity.getProductOrderMaterial().getSaleOrderCode();
                }
            }
            if (StringUtils.isNotBlank(saleOrderNumber)) {
                saleOrderEntity = extSaleOrderInterface.selectSaleOrderByNumber(SaleOrderDetailDTO.builder().saleOrderNumber(saleOrderNumber).build());
            }
            //销售订单
            if (saleOrderEntity != null && CollectionUtils.isNotEmpty(saleOrderEntity.getSaleOrderMaterials())) {
                List<SaleOrderMaterialEntity> saleOrderMaterials = saleOrderEntity.getSaleOrderMaterials();
                for (SaleOrderMaterialEntity saleOrderMaterial : saleOrderMaterials) {
                    OrderTraceVO saleVo = OrderTraceVO.builder()
                            .orderType(OrderNumTypeEnum.SALE_ORDER.getTypeCode())
                            .orderTypeName(OrderNumTypeEnum.SALE_ORDER.getTypeName())
                            .orderId(saleOrderEntity.getSaleOrderId())
                            .orderNumber(saleOrderEntity.getSaleOrderNumber())
                            .state(saleOrderEntity.getState())
                            .stateName(saleOrderEntity.getStateName())
                            .materialCode(saleOrderMaterial.getMaterialCode())
                            .materialName(saleOrderMaterial.getMaterialFields().getName())
                            .build();
                    traceVOS.add(saleVo);
                }
            }
            if (productOrderEntity != null) {
                //生产订单
                OrderTraceVO productVo = OrderTraceVO.builder()
                        .orderType(OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode())
                        .orderTypeName(OrderNumTypeEnum.PRODUCT_ORDER.getTypeName())
                        .build();
                String materialCode = productOrderEntity.getProductOrderMaterial().getMaterialCode();
                productVo.setOrderNumber(productOrderEntity.getProductOrderNumber());
                productVo.setOrderId(productOrderEntity.getProductOrderId());
                productVo.setState(productOrderEntity.getState());
                productVo.setStateName(productOrderEntity.getStateName());
                productVo.setMaterialCode(materialCode);
                if (StringUtils.isNotBlank(materialCode)) {
                    MaterialEntity materialEntity = materialService.lambdaQuery().eq(MaterialEntity::getCode, materialCode).last("limit 1").one();
                    productVo.setMaterialName(materialEntity == null ? null : materialEntity.getName());
                }
                traceVOS.add(productVo);
            }
        }
        return OrderTraceGroupVO.builder().upstreamOrders(traceVOS).build();
    }

    @Override
    public void verifyFormat(List<WorkOrderExcelDTO> workOrderExcelDTOS) {
        boolean canImport;
        StringBuilder importResult;
        Date createTime = new Date();
        List<String> workOrderNumberList = new ArrayList<>();
        for (WorkOrderExcelDTO workOrderExcelDTO : workOrderExcelDTOS) {
            importResult = new StringBuilder();
            canImport = true;

            Pattern p = Pattern.compile("^[\\w.-]+$");
            //校验工单编号
            if (StringUtils.isBlank(workOrderExcelDTO.getWorkOrderNumber())
                    || !p.matcher(workOrderExcelDTO.getWorkOrderNumber()).matches()) {
                importResult.append("生产工单编号字段不合法；");
                canImport = false;
            }
            if (workOrderNumberList.contains(workOrderExcelDTO.getWorkOrderNumber())) {
                importResult.append("excel中已存在相同的工单号；");
                canImport = false;
            } else {
                workOrderNumberList.add(workOrderExcelDTO.getWorkOrderNumber());
            }
            MaterialEntity materialEntity = null;
            if (StringUtils.isBlank(workOrderExcelDTO.getMaterialCode())
                    || !p.matcher(workOrderExcelDTO.getMaterialCode()).matches()) {
                importResult.append("物料编码字段不合法；");
                canImport = false;
            } else {
                materialEntity = materialService.getSimpleMaterialByCode(workOrderExcelDTO.getMaterialCode());
                if (materialEntity == null) {
                    importResult.append("物料编码字段不合法,请检查物料编码是否存在；");
                    canImport = false;
                } else {
                    workOrderExcelDTO.setMaterialName(materialEntity.getName());
                    workOrderExcelDTO.setUnit(materialEntity.getComp());
                }
            }
            // 单据类型
            if (StringUtils.isBlank(workOrderExcelDTO.getOrderTypeName())) {
                OrderTypeInfoVO defaultOrderTypeVO = orderTypeConfigService.getDefaultOrderTypeCodeByCategoryCode(CategoryTypeEnum.WORK_ORDER.getTypeCode());
                workOrderExcelDTO.setOrderType(defaultOrderTypeVO.getOrderType());
                workOrderExcelDTO.setBusinessType(defaultOrderTypeVO.getBusinessTypeCode());
//                workOrderExcelDTO.setOrderType(WorkOrderTypeEnum.NORMAL.getCode());
            } else {
                OrderTypeInfoVO OrderTypeVO = orderTypeConfigService.getOrderTypeByCategoryCodeAndOrderTypeName(CategoryTypeEnum.WORK_ORDER.getTypeCode(), workOrderExcelDTO.getOrderTypeName());
                if (OrderTypeVO == null) {
                    importResult.append("未找到对应的单据类型；");
                    canImport = false;
                } else {
                    workOrderExcelDTO.setOrderType(OrderTypeVO.getOrderType());
                    workOrderExcelDTO.setBusinessType(OrderTypeVO.getBusinessTypeCode());
                }
//                workOrderExcelDTO.setOrderType(WorkOrderTypeEnum.getCodeByName(workOrderExcelDTO.getOrderTypeName()) == null ? WorkOrderTypeEnum.NORMAL.getCode() : WorkOrderTypeEnum.getCodeByName(workOrderExcelDTO.getOrderTypeName()));
            }
            //业务单元编码
            if (!StringUtils.isEmpty(workOrderExcelDTO.getBusinessUnitCode())) {
                List<BusinessUnitVO> businessUnitVOS = businessUnitService.selectList(BusinessUnitSelectDTO.builder()
                        .fullCode(workOrderExcelDTO.getBusinessUnitCode())
                        .state(BusinessUnitStateEnum.RELEASED)
                        .build());
                if (CollectionUtils.isEmpty(businessUnitVOS)) {
                    importResult.append("业务单元编码不存在系统中或不生效；");
                    canImport = false;
                } else {
                    workOrderExcelDTO.setBusinessUnitName(businessUnitVOS.get(0).getName());
                }

            }

            //客户编码、客户名称
            if (StringUtils.isNotBlank(workOrderExcelDTO.getCustomerCode())) {
                CustomerEntity customerEntity = customerService.lambdaQuery()
                        .eq(CustomerEntity::getCustomerCode, workOrderExcelDTO.getCustomerCode())
                        .one();
                if (Objects.isNull(customerEntity)) {
                    importResult.append("客户编码不存在系统中；");
                    canImport = false;
                } else {
                    if (StringUtils.isNotBlank(workOrderExcelDTO.getCustomerName()) && !workOrderExcelDTO.getCustomerName().equals(customerEntity.getCustomerName())) {
                        importResult.append("客户编码和客户名称不匹配；");
                        canImport = false;
                    }
                    if (StringUtils.isBlank(workOrderExcelDTO.getCustomerName())) {
                        workOrderExcelDTO.setCustomerName(customerEntity.getCustomerName());
                    }
                }
            } else if (StringUtils.isBlank(workOrderExcelDTO.getCustomerCode()) && StringUtils.isNotBlank(workOrderExcelDTO.getCustomerName())) {
                // 只填了客户名称
                CustomerEntity customerEntity = customerService.lambdaQuery()
                        .eq(CustomerEntity::getCustomerName, workOrderExcelDTO.getCustomerName())
                        .last("limit 1").one();
                if (Objects.isNull(customerEntity)) {
                    importResult.append("客户名称不存在系统中；");
                    canImport = false;
                } else {
                    workOrderExcelDTO.setCustomerCode(customerEntity.getCustomerCode());
                }
            }
            // 包装方案
            if (StringUtils.isNotBlank(workOrderExcelDTO.getPackageSchemeCode())) {
                PackageSchemeEntity packageScheme = packageSchemeService.getDetailByCode(workOrderExcelDTO.getPackageSchemeCode());
                if (Objects.isNull(packageScheme)) {
                    importResult.append("包装方案不存在系统中；");
                    canImport = false;
                }
            }
            // 计划批数、每批计划数
            if (Objects.nonNull(workOrderExcelDTO.getPlannedBatches()) && workOrderExcelDTO.getPlannedBatches() < 0) {
                importResult.append("计划批数必须是正数；");
                canImport = false;
            }
            if (Objects.nonNull(workOrderExcelDTO.getPlansPerBatch()) && workOrderExcelDTO.getPlansPerBatch() < 0) {
                importResult.append("每批计划数必须是正数；");
                canImport = false;
            }
            if (StringUtils.isBlank(workOrderExcelDTO.getWorkCenterName())) {
                importResult.append("工作中心不能为空;");
                canImport = false;
            } else {
                LambdaQueryWrapper<WorkCenterEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(WorkCenterEntity::getName, workOrderExcelDTO.getWorkCenterName());
                WorkCenterEntity workCenterEntity = workCenterMapper.selectOne(lambdaQueryWrapper);
                if (workCenterEntity == null) {
                    importResult.append("工作中心字段不合法;");
                    canImport = false;
                } else {
                    workOrderExcelDTO.setWorkCenterId(workCenterEntity.getId());
                }
                if (StringUtils.isNotBlank(workOrderExcelDTO.getCraftCode()) && StringUtils.isNotBlank(workOrderExcelDTO.getMaterialCode())) {
                    // 工艺编码：1.如果物料没有工艺,可以使用工艺模板; 2.如果物料有工艺,一定要使用物料绑定的工艺
                    List<com.yelink.dfs.entity.product.CraftEntity> craftEntityList = craftService.getListByMaterialCode(workOrderExcelDTO.getMaterialCode(), workOrderExcelDTO.getBusinessType());
                    craftEntityList = CollectionUtils.isEmpty(craftEntityList) ? new ArrayList<>() : craftEntityList.stream().filter(res -> res.getCraftCode().equals(workOrderExcelDTO.getCraftCode())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(craftEntityList)) {
                        importResult.append("物料对应生效的工艺编码不存在;");
                        canImport = false;
                    } else {
                        workOrderExcelDTO.setCraftId(craftEntityList.get(0).getCraftId());
                    }
                }
                if (StringUtils.isNotBlank(workOrderExcelDTO.getProcedureName())) {
                    // 导入工序,工艺编码不能为空
                    if (Objects.isNull(workOrderExcelDTO.getCraftId())) {
                        importResult.append("工艺编码不能为空;");
                        canImport = false;
                    } else {
                        List<String> craftProcedureNames = Arrays.stream(workOrderExcelDTO.getProcedureName().split(Constant.SEP)).collect(Collectors.toList());
                        List<CraftProcedureEntity> craftProcedureEntities = craftProcedureService.lambdaQuery()
                                .in(CraftProcedureEntity::getProcedureName, craftProcedureNames)
                                .eq(CraftProcedureEntity::getCraftId, workOrderExcelDTO.getCraftId())
                                .list();
                        if (CollectionUtils.isEmpty(craftProcedureEntities)) {
                            importResult.append("工序不存在或工艺与工序不匹配;");
                            canImport = false;
                        } else {
                            // 所选工序必须为同一工序组
                            long count = craftProcedureEntities.stream().map(CraftProcedureEntity::getCraftProcedureGroup).distinct().count();
                            if (count > 1) {
                                importResult.append("所选工序必须为同一工序组;");
                                canImport = false;
                            } else {
                                workOrderExcelDTO.setRelateCraftProcedures(craftProcedureEntities);
                            }
                            // 同一工序组,工作中心相同,随便找一个工序判断工作中心是否匹配
                            CraftProcedureEntity craftProcedureEntity = craftProcedureEntities.get(0);
                            if (StringUtils.isNotBlank(workOrderExcelDTO.getWorkCenterName())) {
                                if (craftProcedureEntity.getWorkCenterNames() == null) {
                                    importResult.append("工序的制造单元模型与导入的工作中心不匹配;");
                                    canImport = false;
                                } else if (!craftProcedureEntity.getWorkCenterNames().contains(workOrderExcelDTO.getWorkCenterName())) {
                                    importResult.append("工序的制造单元模型与导入的工作中心不匹配;");
                                    canImport = false;
                                }
                            }
                        }
                    }
                }
                if (workOrderExcelDTO.getPlanQuantity() == null || workOrderExcelDTO.getPlanQuantity() < 0) {
                    importResult.append("计划数量字段不合法;");
                    canImport = false;
                }

                if (workOrderExcelDTO.getStartDate() == null) {
                    importResult.append("计划开始时间不合法;");
                    canImport = false;
                }
                if (workOrderExcelDTO.getEndDate() == null) {
                    importResult.append("计划完成时间字段不合法;");
                    canImport = false;
                }
                List<String> typeNames = Arrays.asList(OrderNumTypeEnum.PRODUCT_ORDER.getTypeName(), OrderNumTypeEnum.SALE_ORDER.getTypeName());
                if (StringUtils.isNotBlank(workOrderExcelDTO.getRelatedOrderType()) && !typeNames.contains(workOrderExcelDTO.getRelatedOrderType())) {
                    importResult.append("关联订单类型字段不合法;");
                    canImport = false;
                }
                // 派工状态
                List<String> assignmentStateNames = Arrays.asList(AssignmentStateEnum.ASSIGNED.getTypeName(), AssignmentStateEnum.TO_BE_ASSIGNED.getTypeName());
                if (StringUtils.isNotBlank(workOrderExcelDTO.getAssignmentStateName()) && !assignmentStateNames.contains(workOrderExcelDTO.getAssignmentStateName())) {
                    importResult.append("派工状态只能是 待派工或者已派工;");
                    canImport = false;
                }
                // 燕生说创建状态固定给待派工，无论导入的是什么派工状态数据
                if (StringUtils.isNotBlank(workOrderExcelDTO.getStateName()) && workOrderExcelDTO.getStateName().equals(WorkOrderStateEnum.CREATED.getName())) {
                    workOrderExcelDTO.setAssignmentStateName(AssignmentStateEnum.TO_BE_ASSIGNED.getTypeName());
                    workOrderExcelDTO.setAssignmentState(AssignmentStateEnum.TO_BE_ASSIGNED.getType());
                }
                // 派工状态如果为空取默认的派工业务配置
                if (StringUtils.isBlank(workOrderExcelDTO.getAssignmentStateName())) {
                    FullPathCodeDTO dto = FullPathCodeDTO.builder()
                            .fullPathCode(ConfigConstant.WORK_ORDER_ASSIGNMENT_CONFIG).build();
                    WorkOrderAssignmentConfigDTO config = businessConfigService.getValueDto(dto, WorkOrderAssignmentConfigDTO.class);
                    workOrderExcelDTO.setAssignmentState(config.getAssignmentState());
                }
                //关联订单
                if (typeNames.contains(workOrderExcelDTO.getRelatedOrderType()) && StringUtils.isNotBlank(workOrderExcelDTO.getRelatedOrder())) {
                    if (!isOrderExist(OrderNumTypeEnum.getCodeByName(workOrderExcelDTO.getRelatedOrderType()), workOrderExcelDTO.getRelatedOrder())) {
                        importResult.append("关联单据在系统中未找到;");
                        canImport = false;
                    } else {
                        // 如果关联了单据，就必须关联单据行号且和所关联的行号和销售订单、物料对应
                        if (workOrderExcelDTO.getRelatedOrderType().equals(OrderNumTypeEnum.SALE_ORDER.getTypeName())) {
                            SaleOrderEntity saleOrderEntity = extSaleOrderInterface.selectSaleOrderByNumber(SaleOrderDetailDTO.builder().saleOrderNumber(workOrderExcelDTO.getRelatedOrder()).build());
                            boolean isMatched = saleOrderEntity.getSaleOrderMaterials().stream().anyMatch(o -> o.getLineNumber().equals(workOrderExcelDTO.getRelatedSaleOrderMaterialLineNumber()) && o.getMaterialCode().equals(workOrderExcelDTO.getMaterialCode()));
                            if (!isMatched) {
                                importResult.append("关联的单据物料行号在系统中不匹配;");
                                canImport = false;
                            }
                        } else if (workOrderExcelDTO.getRelatedOrderType().equals(OrderNumTypeEnum.PRODUCT_ORDER.getTypeName())) {
                            ProductOrderEntity productOrderEntity = extProductOrderInterface.selectProductOrderByNumber(ProductOrderDetailDTO.builder().productOrderNumber(workOrderExcelDTO.getRelatedOrder()).build());
                            boolean isMatched = productOrderEntity.getProductOrderMaterials().stream().anyMatch(o -> o.getLineNumber().equals(workOrderExcelDTO.getRelatedProductOrderMaterialLineNumber()) && o.getMaterialCode().equals(workOrderExcelDTO.getMaterialCode()));
                            if (!isMatched) {
                                importResult.append("关联的单据物料行号在系统中不匹配;");
                                canImport = false;
                            }
                        }
                    }
                }

                //生产订单订单状态不限制类型 -- [2.5版本]饶燕生口头需求
                workOrderExcelDTO.setState(WorkOrderStateEnum.getCodeByName(workOrderExcelDTO.getStateName()));

                if (workOrderExcelDTO.getState() == null) {
                    importResult.append("无状态数据不导入；");
                    canImport = false;
                } else {
                    // 只能导入创建、生效状态
                    if (!(WorkOrderStateEnum.CREATED.getCode().equals(workOrderExcelDTO.getState()) || WorkOrderStateEnum.RELEASED.getCode().equals(workOrderExcelDTO.getState()))) {
                        importResult.append("只支持导入创建和生效状态;");
                        canImport = false;
                    }
                }
                if (canImport && WorkCenterTypeEnum.TEAM.getCode().equals(workCenterEntity.getType())) {
                    if (StringUtils.isNotBlank(workOrderExcelDTO.getTeamName())) {
//                        SysTeamEntity sysTeamEntity = sysTeamService.getTeamByName(workOrderExcelDTO.getTeamName());
                        List<String> teamNames = Arrays.asList(workOrderExcelDTO.getTeamName().split(Constants.SEP));
                        List<SysTeamEntity> sysTeamEntities = sysTeamService.lambdaQuery().in(SysTeamEntity::getTeamName, teamNames).list();
                        if (sysTeamEntities.size() != teamNames.size()) {
                            importResult.append("填写的班组中存在系统中不存在的数据;");
                            canImport = false;
                        } else {
                            List<Integer> teamIds = sysTeamEntities.stream().map(SysTeamEntity::getId).collect(Collectors.toList());
                            List<WorkCenterTeamEntity> workCenterTeamEntities = workCenterTeamService.lambdaQuery().in(WorkCenterTeamEntity::getTeamId, teamIds).list();
                            int workCenterSize = workCenterTeamEntities.stream().collect(Collectors.groupingBy(WorkCenterTeamEntity::getWorkCenterId)).size();
                            if (workCenterSize > 1) {
                                importResult.append("关联的班组不属于同一个工作中心;");
                                canImport = false;
                            } else {
                                Integer workCenterId = workCenterTeamEntities.get(0).getWorkCenterId();
                                if (!workCenterId.equals(workCenterEntity.getId())) {
                                    importResult.append("关联的班组不属于该工作中心;");
                                    canImport = false;
                                } else {
                                    List<WorkOrderBasicUnitRelationInsertDTO> basicUnitRelationInsertDTOS = teamIds.stream().map(teamId -> WorkOrderBasicUnitRelationInsertDTO.builder()
                                            .productionBasicUnitId(teamId)
                                            .workCenterId(workCenterId).build()).collect(Collectors.toList());
                                    workOrderExcelDTO.setProductBasicUnits(basicUnitRelationInsertDTOS);
                                }
                            }
                        }
                    } else {
                        importResult.append("工作中心关联的是班组，班组名称必填;");
                        canImport = false;
                    }
                    if (StringUtils.isNotBlank(workOrderExcelDTO.getRelevanceDeviceNames())) {
                        List<Integer> relevanceDeviceIds = new ArrayList<>();
                        String[] split = workOrderExcelDTO.getRelevanceDeviceNames().split(Constant.SEP);
                        for (String deviceName : split) {
                            List<DeviceEntity> deviceList = deviceService.lambdaQuery().eq(DeviceEntity::getDeviceName, deviceName).list();
                            if (CollectionUtils.isEmpty(deviceList)) {
                                importResult.append("系统未找到" + deviceName + "设备名称;");
                                canImport = false;
                            } else if (deviceList.size() > 1) {
                                importResult.append("系统找到多个" + deviceName + "设备名称;");
                                canImport = false;
                            } else {
                                relevanceDeviceIds.add(deviceList.get(0).getDeviceId());
                            }
                        }
                        workOrderExcelDTO.setRelevanceDeviceIds(relevanceDeviceIds);
                    }
                } else if (canImport && WorkCenterTypeEnum.LINE.getCode().equals(workCenterEntity.getType())) {
                    if (StringUtils.isNotBlank(workOrderExcelDTO.getLineName())) {
                        List<String> lineNames = Arrays.asList(workOrderExcelDTO.getLineName().split(Constants.SEP));
                        if (lineNames.size() > 1) {
                            importResult.append("只能填写一个制造单元名称;");
                            canImport = false;
                        } else {
                            com.yelink.dfs.entity.manufacture.ProductionLineEntity lineEntity = getProductionLineByName(workOrderExcelDTO.getLineName());
                            if (lineEntity == null) {
                                importResult.append("系统未找到该制造单元名称;");
                                canImport = false;
                            } else {
                                List<WorkOrderBasicUnitRelationInsertDTO> basicUnitRelationInsertDTOS = Collections.singletonList(
                                        WorkOrderBasicUnitRelationInsertDTO.builder()
                                                .productionBasicUnitId(lineEntity.getProductionLineId())
                                                .workCenterId(workCenterEntity.getId()).build());
                                workOrderExcelDTO.setProductBasicUnits(basicUnitRelationInsertDTOS);
                            }
                        }
                    } else {
                        importResult.append("工作中心关联的是制造单元，制造单元名称必填;");
                        canImport = false;
                    }
                    if (StringUtils.isNotBlank(workOrderExcelDTO.getRelevanceTeamNames())) {
                        List<Integer> relevanceTeamIds = new ArrayList<>();
                        String[] split = workOrderExcelDTO.getRelevanceTeamNames().split(Constant.SEP);
                        for (String teamName : split) {
                            SysTeamEntity sysTeamEntity = sysTeamService.getTeamByName(teamName);
                            if (sysTeamEntity == null) {
                                importResult.append("系统未找到" + teamName + "班组名称;");
                                canImport = false;
                            } else {
                                relevanceTeamIds.add(sysTeamEntity.getId());
                            }
                        }
                        workOrderExcelDTO.setRelevanceTeamIds(relevanceTeamIds);
                    }
                } else if (canImport && WorkCenterTypeEnum.DEVICE.getCode().equals(workCenterEntity.getType())) {
                    if (StringUtils.isNotBlank(workOrderExcelDTO.getDeviceName())) {
                        List<String> deviceNames = Arrays.asList(workOrderExcelDTO.getDeviceName().split(Constants.SEP));
                        List<DeviceEntity> deviceEntities = deviceService.lambdaQuery().in(DeviceEntity::getDeviceName, deviceNames).list();
                        if (deviceEntities.size() != deviceNames.size()) {
                            importResult.append("填写的设备中存在系统中不存在的数据;");
                            canImport = false;
                        } else {
                            List<Integer> deviceIds = deviceEntities.stream().map(DeviceEntity::getDeviceId).collect(Collectors.toList());
                            List<WorkCenterDeviceEntity> workCenterDeviceEntities = workCenterDeviceService.lambdaQuery()
                                    .in(WorkCenterDeviceEntity::getDeviceId, deviceIds).list();
                            int workCenterSize = workCenterDeviceEntities.stream().collect(Collectors.groupingBy(WorkCenterDeviceEntity::getWorkCenterId)).size();
                            if (workCenterSize > 1) {
                                importResult.append("关联的设备不属于同一个工作中心;");
                                canImport = false;
                            } else {
                                Integer workCenterId = workCenterDeviceEntities.get(0).getWorkCenterId();
                                if (!workCenterId.equals(workCenterEntity.getId())) {
                                    importResult.append("关联的设备不属于该工作中心;");
                                    canImport = false;
                                } else {
                                    List<WorkOrderBasicUnitRelationInsertDTO> basicUnitRelationInsertDTOS = deviceIds.stream().map(teamId -> WorkOrderBasicUnitRelationInsertDTO.builder()
                                            .productionBasicUnitId(teamId)
                                            .workCenterId(workCenterId).build()).collect(Collectors.toList());
                                    workOrderExcelDTO.setProductBasicUnits(basicUnitRelationInsertDTOS);
                                }
                            }
                        }
//                        }
                    } else {
                        importResult.append("工作中心关联的是设备，设备名称必填;");
                        canImport = false;
                    }
                    if (StringUtils.isNotBlank(workOrderExcelDTO.getRelevanceTeamNames())) {
                        List<Integer> relevanceTeamIds = new ArrayList<>();
                        String[] split = workOrderExcelDTO.getRelevanceTeamNames().split(Constant.SEP);
                        for (String teamName : split) {
                            SysTeamEntity sysTeamEntity = sysTeamService.getTeamByName(teamName);
                            if (sysTeamEntity == null) {
                                importResult.append("系统未找到" + teamName + "班组名称;");
                                canImport = false;
                            } else {
                                relevanceTeamIds.add(sysTeamEntity.getId());
                            }
                        }
                        workOrderExcelDTO.setRelevanceTeamIds(relevanceTeamIds);
                    }
                }
            }
            //当工单存在时校验
            verifyExistNumber(workOrderExcelDTO, importResult, canImport);
            if (workOrderExcelDTO.getVerifyPass()) {
                workOrderExcelDTO.setCreateDate(createTime);
                workOrderExcelDTO.setImportResult("数据校验通过");
            } else {
                workOrderExcelDTO.setImportResult(String.valueOf(importResult));
            }
        }
    }

    @Override
    public void pushToTask(boolean register, WorkOrderTaskDTO taskDTO) {
        WorkOrderService workOrderService = SpringUtil.getBean(WorkOrderService.class);
        WorkOrderEntity workOrderEntity = workOrderService.lambdaQuery().eq(WorkOrderEntity::getWorkOrderNumber, taskDTO.getWorkOrderNumber()).one();
        if (Objects.isNull(workOrderEntity)) {
            return;
        }
        WorkOrderStateEnum stateEnum = WorkOrderStateEnum.getByCode(workOrderEntity.getState());
        // 获取任务用户
        TaskUserVO taskUserVO = getTaskUserVO(workOrderEntity, taskDTO);
        // 组装任务
        TaskUpsertDTO task = TaskUpsertDTO.builder()
                .orderCategory(OrderCategoryEnum.WORK_ORDER.getCode())
                .orderNumber(taskDTO.getWorkOrderNumber())
                .orderId(String.valueOf(workOrderEntity.getWorkOrderId()))
                .upstreamOrderCategory(workOrderEntity.getProductOrderNumber() == null ? null : OrderCategoryEnum.PRODUCT_ORDER.getCode())
                .upstreamOrderNumber(workOrderEntity.getProductOrderNumber())
                .taskProgress(progress(workOrderEntity))
                .userVO(taskUserVO)
                .materialCode(workOrderEntity.getMaterialCode())
                .operator(Optional.ofNullable(workOrderEntity.getUpdateBy()).orElse(workOrderEntity.getCreateBy()))
                .log(taskDTO.getLog())
                .build();
        if (stateEnum != null) {
            task.setOrderState(String.valueOf(stateEnum.getCode()));
            task.setOrderStateName(stateEnum.getName());
            switch (stateEnum) {
                case CREATED:
                case RELEASED:
                case INVESTMENT:
                case HANG_UP:
                    task.setTaskState(TaskStateEnum.IN_PROGRESS.getCode());
                    break;
                case FINISHED:
                case CLOSED:
                case CANCELED:
                    task.setTaskState(TaskStateEnum.COMPLETE.getCode());
                    break;
            }
        }
        if (register) {
            taskRegister.upsertTask(task);
            if (StringUtils.isNotBlank(taskDTO.getRelateType()) && taskDTO.getRelateType().equals(TaskRelationTypeEnum.WORK_ORDER_REPORT.getTypeCode())) {
                TaskRelationUpsertDTO taskRelationUpsertDTO = TaskRelationUpsertDTO.builder()
                        .orderCategory(OrderCategoryEnum.WORK_ORDER.getCode())
                        .orderNumber(taskDTO.getWorkOrderNumber())
                        .relateType(taskDTO.getRelateType())
                        .relateId(taskDTO.getRelateId())
                        .build();
                taskRegister.upsertTaskRelation(taskRelationUpsertDTO);
            }
        } else {
            taskRegister.deleteTask(JacksonUtil.convertObject(task, TaskDeleteDTO.class));
        }
    }

    @Override
    public OrderTypeInfoVO getOrderTypeByProductOrder(ProductOrderParamDTO paramDTO) {
        ProductOrderEntity productOrderEntity = extProductOrderInterface.selectProductOrderByNumber(ProductOrderDetailDTO.builder().productOrderNumber(paramDTO.getProductOrderNumber()).build());
        // 判断生产订单是不是试产订单的业务类型下的单据类型，如果是则默认拿试产工单的业务类型下的单据类型
        List<BusinessTypeListVO> vos = orderTypeConfigService.getOrderTypeListByBusinessType(OrderTypeSelectDTO.builder()
                .businessCode(BusinessTypeEnum.TEST_PRODUCT_ORDER.getTypeCode())
                .showType(OrderTypeShowTypeEnum.SHOW_ALL.getCode())
                .build());
        List<String> businessCodes = vos.stream().map(BusinessTypeListVO::getBusinessTypeCode).collect(Collectors.toList());
        OrderTypeInfoVO defaultOrderType;
        if (businessCodes.contains(productOrderEntity.getType())) {
            defaultOrderType = orderTypeConfigService.getDefaultOrderTypeCodeByBusinessCode(BusinessTypeEnum.TEST_WORK_ORDER.getTypeCode());
            defaultOrderType.setIsEdit(false);
        } else {
            // 如果不是试产订单，则默认拿默认业务类型下的默认单据类型
            defaultOrderType = orderTypeConfigService.getDefaultOrderTypeCodeByCategoryCode(CategoryTypeEnum.WORK_ORDER.getTypeCode());
            defaultOrderType.setIsEdit(true);
        }
        return defaultOrderType;
    }

    /**
     * 获取任务用户
     */
    private TaskUserVO getTaskUserVO(WorkOrderEntity workOrderEntity, WorkOrderTaskDTO taskDTO) {
        List<String> magNames = new ArrayList<>();
        List<TaskUserVO.UserVO> employeeUserVOs = new ArrayList<>();
        List<WorkOrderBasicUnitRelationEntity> relationEntities = basicUnitRelationService.getByWorkOrderNumber(workOrderEntity.getWorkOrderNumber());
        List<Integer> basicUnitIds = relationEntities.stream().map(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitId).collect(Collectors.toList());
        // 找到生产基本单元对应的负责人信息
        if (CollectionUtils.isNotEmpty(basicUnitIds)) {
            if (workOrderEntity.getWorkCenterType().equals(WorkCenterTypeEnum.LINE.getCode())) {
                ProductionLineService productionLineService = SpringUtil.getBean(ProductionLineService.class);
                magNames = productionLineService.lambdaQuery().in(com.yelink.dfs.entity.manufacture.ProductionLineEntity::getProductionLineId, basicUnitIds)
                        .list().stream()
                        .map(com.yelink.dfs.entity.manufacture.ProductionLineEntity::getMagName)
                        .collect(Collectors.toList());
                // 制造单元绑定的员工需要转化为参与人
                employeeUserVOs = lineEmployeeService.lambdaQuery()
                        .in(LineEmployeeEntity::getLineId, basicUnitIds)
                        .list().stream().map(user -> TaskUserVO.UserVO.builder().username(user.getUserName()).build())
                        .collect(Collectors.toList());
            } else if (workOrderEntity.getWorkCenterType().equals(WorkCenterTypeEnum.TEAM.getCode())) {
                magNames = sysTeamService.lambdaQuery().in(SysTeamEntity::getId, basicUnitIds)
                        .list().stream()
                        .map(SysTeamEntity::getLeaderName)
                        .collect(Collectors.toList());
            }
        }
        // 组转负责人
        List<TaskUserVO.UserVO> directors = magNames.stream().map(username ->
                        TaskUserVO.UserVO.builder().username(username).build())
                .collect(Collectors.toList());
        // 组转参与人
        List<TaskUserVO.UserVO> players = new ArrayList<>(Arrays.asList(
                TaskUserVO.UserVO.builder().username(workOrderEntity.getCreateBy()).build(),
                TaskUserVO.UserVO.builder().username(workOrderEntity.getUpdateBy()).build(),
                TaskUserVO.UserVO.builder().username(workOrderEntity.getApprover()).build(),
                TaskUserVO.UserVO.builder().username(workOrderEntity.getActualApprover()).build()
        ));
        if (StringUtils.isNotBlank(taskDTO.getReporter())) {
            players.add(TaskUserVO.UserVO.builder().username(taskDTO.getReporter()).build());
        }
        if (StringUtils.isNotBlank(taskDTO.getBarCodeCreateBy())) {
            players.add(TaskUserVO.UserVO.builder().username(taskDTO.getBarCodeCreateBy()).build());
        }
        if (StringUtils.isNotBlank(taskDTO.getFlowCodeCreateBy())) {
            players.add(TaskUserVO.UserVO.builder().username(taskDTO.getFlowCodeCreateBy()).build());
        }
        players.addAll(employeeUserVOs);
        return TaskUserVO.builder().players(players).directors(directors).build();
    }

    private double progress(WorkOrderEntity workOrderEntity) {
        if (workOrderEntity.getPlanQuantity() == 0) {
            return 0d;
        }
        return NullableDouble.of(workOrderEntity.getFinishCount()).div(workOrderEntity.getPlanQuantity()).scale(4).upper(1).cal(0);
    }

    /**
     * 订单是否存在
     *
     * @param orderType
     * @param orderNumber
     * @return
     */
    private boolean isOrderExist(String orderType, String orderNumber) {
        if (orderType.equals(OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode())) {
            // 推送至kafka，获取生产订单数据
            ProductOrderEntity productOrderEntity = extProductOrderInterface.selectProductOrderByNumber(ProductOrderDetailDTO.builder().productOrderNumber(orderNumber).build());
            return productOrderEntity != null;
        }

        if (orderType.equals(OrderNumTypeEnum.SALE_ORDER.getTypeCode())) {
            SaleOrderEntity saleOrderEntity = extSaleOrderInterface.selectSaleOrderByNumber(SaleOrderDetailDTO.builder().saleOrderNumber(orderNumber).build());
            return saleOrderEntity != null;
        }

        return false;
    }

    /**
     * 通过名称查询制造单元
     *
     * @param lineName
     * @return
     */
    private com.yelink.dfs.entity.manufacture.ProductionLineEntity getProductionLineByName(String lineName) {
        LambdaQueryWrapper<com.yelink.dfs.entity.manufacture.ProductionLineEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(com.yelink.dfs.entity.manufacture.ProductionLineEntity::getProductionLineId, com.yelink.dfs.entity.manufacture.ProductionLineEntity::getName,
                        com.yelink.dfs.entity.manufacture.ProductionLineEntity::getProductionLineCode)
                .eq(com.yelink.dfs.entity.manufacture.ProductionLineEntity::getName, lineName).last("limit 1");
        return productionLineMapper.selectOne(wrapper);
    }

    private void verifyExistNumber(WorkOrderExcelDTO workOrderExcelDTO, StringBuilder importResult, boolean canImport) {
        WorkOrderEntity entity = this.lambdaQuery().eq(WorkOrderEntity::getWorkOrderNumber, workOrderExcelDTO.getWorkOrderNumber()).one();
        //工单不为空
        if (entity != null) {
            //(2) 导入表中数据的状态，等于或大于，系统中对应数据的状态；(例如：创建态不能覆盖生效态)
            Integer state = entity.getState();
            //state为null时报空指针异常，当为null时给state设置默认值避免报错，以便保存导入日志
            Integer dtoState = workOrderExcelDTO.getState() == null ? -1 : workOrderExcelDTO.getState();
            //状态比之前的小,且两个状态不是挂起和投产
            boolean a = dtoState < state;
            boolean b = dtoState.equals(WorkOrderStateEnum.INVESTMENT.getCode()) && state.equals(WorkOrderStateEnum.HANG_UP.getCode());
            if (a && !b) {
                importResult.append("系统数据已存在,且状态不可覆盖。");
                canImport = false;
            }
            //取消状态不可覆盖
            List<Integer> states = Arrays.asList(WorkOrderStateEnum.CREATED.getCode(), WorkOrderStateEnum.RELEASED.getCode(),
                    WorkOrderStateEnum.CANCELED.getCode());
            if (dtoState.equals(WorkOrderStateEnum.CANCELED.getCode()) && !states.contains(state)) {
                importResult.append("取消状态不可覆盖投产之后的状态。");
                canImport = false;
            }
            Date dtoUpdateDate = workOrderExcelDTO.getUpdateDate();
            if (dtoUpdateDate == null) {
                importResult.append("导入系统中已存在的工单,未获取到更新时间或更新时间为空,不作导入。");
                canImport = false;
                //避免导入重复工单时报空指针异常，给dtoUpdateDate赋值
                dtoUpdateDate = new Date();
            }
            Date now = new Date();
            dtoUpdateDate = dtoUpdateDate.after(now) ? now : dtoUpdateDate;
            if (entity.getUpdateDate() != null && dtoUpdateDate.before(entity.getUpdateDate())) {
                //导入表中的更新时间 > 对应数据的创建/更新时间；
                importResult.append("更新时间早于系统中更新时间。");
                canImport = false;
            }
            workOrderExcelDTO.setIsUpdate(true);
            workOrderExcelDTO.setVerifyPass(canImport);
        } else {
            workOrderExcelDTO.setIsUpdate(false);
            workOrderExcelDTO.setVerifyPass(canImport);
        }

    }


    @Override
    public List<CommonType> getBasicUnit(ProductionBasicUnitSelectDTO selectDTO) {
        WorkOrderExtendService workOrderExtendService = SpringUtil.getBean(WorkOrderExtendService.class);
        List<CommonType> list = new ArrayList<>();
        List<Integer> deviceModelIds = new ArrayList<>();
        List<Integer> lineModelIds = new ArrayList<>();
        List<Integer> teamModelIds = new ArrayList<>();
        List<ProductionBasicUnitTypeDTO> basicUnitTypeDTOS = selectDTO.getProductionBasicUnitTypeDTOS();
        // 查询满足条件的生产基本单元类型
        if (CollectionUtils.isNotEmpty(selectDTO.getAids()) || CollectionUtils.isNotEmpty(selectDTO.getWorkCenterIds())) {
            List<CommonType> basicUnitTypes = workOrderExtendService.getBasicUnitType(selectDTO);
            basicUnitTypeDTOS = basicUnitTypes.stream()
                    .flatMap(o -> o.getList().stream()).map(o -> ProductionBasicUnitTypeDTO.builder()
                            .workCenterType(String.valueOf(o.getType()))
                            .productionBasicUnitTypeId((int) o.getId())
                            .build()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(selectDTO.getProductionBasicUnitTypeDTOS())) {
                List<Integer> filterProductBasicUnitTypeIds = selectDTO.getProductionBasicUnitTypeDTOS().stream().map(ProductionBasicUnitTypeDTO::getProductionBasicUnitTypeId).collect(Collectors.toList());
                basicUnitTypeDTOS = basicUnitTypeDTOS.stream().filter(o -> filterProductBasicUnitTypeIds.contains(o.getProductionBasicUnitTypeId())).collect(Collectors.toList());
            }
        }
        if (!CollectionUtils.isEmpty(basicUnitTypeDTOS)) {
            deviceModelIds = basicUnitTypeDTOS.stream().filter(o -> o.getWorkCenterType().equals(WorkCenterTypeEnum.DEVICE.getCode()))
                    .map(ProductionBasicUnitTypeDTO::getProductionBasicUnitTypeId)
                    .collect(Collectors.toList());
            lineModelIds = basicUnitTypeDTOS.stream().filter(o -> o.getWorkCenterType().equals(WorkCenterTypeEnum.LINE.getCode()))
                    .map(ProductionBasicUnitTypeDTO::getProductionBasicUnitTypeId)
                    .collect(Collectors.toList());
            teamModelIds = basicUnitTypeDTOS.stream().filter(o -> o.getWorkCenterType().equals(WorkCenterTypeEnum.TEAM.getCode()))
                    .map(ProductionBasicUnitTypeDTO::getProductionBasicUnitTypeId)
                    .collect(Collectors.toList());
        }
        // 查询设备（需要结合工作中心id进行过滤）
        List<DeviceEntity> deviceEntities = deviceService.lambdaQuery()
                .in(CollectionUtils.isNotEmpty(deviceModelIds), DeviceEntity::getModelId, deviceModelIds).list();
        if (CollectionUtils.isNotEmpty(selectDTO.getWorkCenterIds())) {
            List<Integer> deviceIds = workCenterDeviceService.lambdaQuery().in(WorkCenterDeviceEntity::getWorkCenterId, selectDTO.getWorkCenterIds())
                    .list().stream()
                    .map(WorkCenterDeviceEntity::getDeviceId)
                    .collect(Collectors.toList());
            deviceIds.addAll(workCenterDeviceRelevanceService.lambdaQuery().in(WorkCenterDeviceRelevanceEntity::getWorkCenterId, selectDTO.getWorkCenterIds())
                    .list().stream()
                    .map(WorkCenterDeviceRelevanceEntity::getDeviceId)
                    .collect(Collectors.toList()));
            deviceEntities = deviceEntities.stream().filter(o -> deviceIds.contains(o.getDeviceId())).collect(Collectors.toList());
        }
        List<CommonType> deviceDtos = deviceEntities.stream().map(o ->
                        CommonType.builder().id(o.getDeviceId()).type(o.getModelId()).code(o.getDeviceCode()).name(o.getDeviceName()).build())
                .collect(Collectors.toList());
        CommonType deviceDto = CommonType.builder().type(WorkCenterTypeEnum.DEVICE.getCode()).name(WorkCenterTypeEnum.DEVICE.getName()).list(deviceDtos).build();
        // 查询班组（需要结合工作中心id进行过滤）
        List<SysTeamEntity> teamEntities = sysTeamService.lambdaQuery()
                .in(CollectionUtils.isNotEmpty(teamModelIds), SysTeamEntity::getTeamType, teamModelIds).list();
        if (CollectionUtils.isNotEmpty(selectDTO.getWorkCenterIds())) {
            List<Integer> teamIds = workCenterTeamService.lambdaQuery().in(WorkCenterTeamEntity::getWorkCenterId, selectDTO.getWorkCenterIds())
                    .list().stream()
                    .map(WorkCenterTeamEntity::getTeamId)
                    .collect(Collectors.toList());
            teamIds.addAll(workCenterTeamRelevanceService.lambdaQuery().in(WorkCenterTeamRelevanceEntity::getWorkCenterId, selectDTO.getWorkCenterIds())
                    .list().stream()
                    .map(WorkCenterTeamRelevanceEntity::getTeamId)
                    .collect(Collectors.toList()));
            teamEntities = teamEntities.stream().filter(o -> teamIds.contains(o.getId())).collect(Collectors.toList());
        }
        List<CommonType> teamDtos = teamEntities.stream().map(o ->
                        CommonType.builder().id(o.getId()).type(o.getTeamType()).code(o.getTeamCode()).name(o.getTeamName()).build())
                .collect(Collectors.toList());
        CommonType teamDto = CommonType.builder().type(WorkCenterTypeEnum.TEAM.getCode()).name(WorkCenterTypeEnum.TEAM.getName()).list(teamDtos).build();
        // 查询制造单元（需要结合工作中心id进行过滤）
        List<com.yelink.dfs.entity.manufacture.ProductionLineEntity> lineEntities = productionLineService.lambdaQuery()
                .in(CollectionUtils.isNotEmpty(lineModelIds), com.yelink.dfs.entity.manufacture.ProductionLineEntity::getModelId, lineModelIds).list();
        if (CollectionUtils.isNotEmpty(selectDTO.getWorkCenterIds())) {
            List<Integer> lineIds = productionLineService.lambdaQuery().in(com.yelink.dfs.entity.manufacture.ProductionLineEntity::getWorkCenterId, selectDTO.getWorkCenterIds())
                    .list().stream()
                    .map(com.yelink.dfs.entity.manufacture.ProductionLineEntity::getProductionLineId)
                    .collect(Collectors.toList());
            lineIds.addAll(workCenterLineRelevanceService.lambdaQuery().in(WorkCenterLineRelevanceEntity::getWorkCenterId, selectDTO.getWorkCenterIds())
                    .list().stream()
                    .map(WorkCenterLineRelevanceEntity::getLineId)
                    .collect(Collectors.toList()));
            lineEntities = lineEntities.stream().filter(o -> lineIds.contains(o.getProductionLineId())).collect(Collectors.toList());
        }
        List<CommonType> lineDtos = lineEntities.stream().map(o ->
                        CommonType.builder().id(o.getProductionLineId()).type(o.getModelId()).code(o.getProductionLineCode()).name(o.getName()).build())
                .collect(Collectors.toList());
        CommonType lineDto = CommonType.builder().type(WorkCenterTypeEnum.LINE.getCode()).name(WorkCenterTypeEnum.LINE.getName()).list(lineDtos).build();
        // 条件查询则选择性查询生产资源
        if (CollectionUtils.isNotEmpty(selectDTO.getProductionBasicUnitTypeDTOS())) {
            List<String> workCenterTypes = selectDTO.getProductionBasicUnitTypeDTOS().stream().map(ProductionBasicUnitTypeDTO::getWorkCenterType).distinct().collect(Collectors.toList());
            if (workCenterTypes.contains(WorkCenterTypeEnum.LINE.getCode())) {
                list.add(lineDto);
            }
            if (workCenterTypes.contains(WorkCenterTypeEnum.TEAM.getCode())) {
                list.add(teamDto);
            }
            if (workCenterTypes.contains(WorkCenterTypeEnum.DEVICE.getCode())) {
                list.add(deviceDto);
            }
            return list;
        }
        list.add(lineDto);
        list.add(deviceDto);
        list.add(teamDto);
        return list;
    }
}
