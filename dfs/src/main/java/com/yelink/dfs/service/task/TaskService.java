package com.yelink.dfs.service.task;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yelink.dfs.entity.task.TaskEntity;
import com.yelink.dfs.entity.task.dto.OrderTaskChartVO;
import com.yelink.dfs.entity.task.dto.OrderTaskOverviewVO;
import com.yelink.dfs.entity.task.dto.OrderTaskUpdateChartDTO;
import com.yelink.dfs.entity.task.dto.TaskDeleteDTO;
import com.yelink.dfs.entity.task.dto.TaskPageDTO;
import com.yelink.dfs.entity.task.dto.TaskRelationTreeQueryDTO;
import com.yelink.dfs.entity.task.dto.TaskUpsertDTO;
import com.yelink.dfs.entity.task.vo.TaskRelationTreeVO;
import com.yelink.dfs.service.impl.task.dto.TaskVO;

import java.util.List;


/**
 * @Description:
 * @Author: zengzhengfu
 * @Date: 2024/12/5
 */
public interface TaskService extends IService<TaskEntity> {

    /**
     * 查询单据关系
     *
     * @param dto
     * @return
     */
    Page<TaskEntity> getPage(TaskPageDTO dto);

    /**
     * 添加单据关系
     *
     * @param dto
     */
    void upsert(TaskUpsertDTO dto);


    /**
     * 删除单据关系
     * @param dto
     */
    void delete(TaskDeleteDTO dto);

    OrderTaskChartVO getOrderTaskChart();

    /**
     * 查询任务总览
     *
     * @param
     * @return
     */
    OrderTaskOverviewVO getTaskOverview(TaskPageDTO dto);

    /**
     * 更新单据大类的任务链图表
     *
     * @param
     * @return
     */
    void updateOrderTaskChart(List<OrderTaskUpdateChartDTO> updateChartDTOS);

    /**
     * 获取任务树
     * @param taskId 任务id
     * @return 任务树
     */
    TaskVO taskTree(Integer taskId);

    List<TaskEntity> orderTrace(String orderCategory, String orderNumber);

    /**
     * 查询任务关系树
     * 根据单据类型、单据编号、物料行id查询完整的上下游关系树
     *
     * @param dto 查询参数
     * @return 任务关系树列表
     */
    List<TaskRelationTreeVO> getTaskRelationTree(TaskRelationTreeQueryDTO dto);
}

