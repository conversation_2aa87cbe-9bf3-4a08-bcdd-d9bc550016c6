package com.yelink.dfs.service.common.config;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownIdentifierStateEnum;
import com.yelink.dfscommon.dto.common.config.FullPathCodeDTO;
import com.yelink.dfscommon.dto.common.config.PushDownFullPathCodeDTO;
import com.yelink.dfscommon.dto.dfs.PushDownIdentifierInfoDTO;
import com.yelink.dfscommon.dto.dfs.PushDownIdentifierTreeDTO;
import com.yelink.dfscommon.entity.dfs.OrderPushDownIdentifierEntity;

import java.util.List;
import java.util.Map;

/**
 * 单据下推标识服务接口
 *
 * <AUTHOR>
 */
public interface OrderPushDownIdentifierService extends IService<OrderPushDownIdentifierEntity> {

    /**
     * 根据源单据类型和物料行id查询下推标识
     *
     * @param orderType 源单据类型
     * @param orderMaterialId 单据物料行id
     * @return 下推标识列表
     */
    List<OrderPushDownIdentifierEntity> getByOrderTypeAndMaterialId(String orderType, String orderMaterialId);

    /**
     * 根据源单据类型、物料行id和批次号查询下推标识
     *
     * @param orderType 源单据类型
     * @param orderMaterialId 单据物料行id
     * @param batchNumber 批次号
     * @return 下推标识
     */
    OrderPushDownIdentifierEntity getByOrderTypeAndMaterialIdAndBatch(String orderType, String orderMaterialId, String batchNumber);

    /**
     * 根据下推标识状态查询
     *
     * @param state 下推标识状态
     * @return 下推标识列表
     */
    List<OrderPushDownIdentifierEntity> getByState(String state);

    /**
     * 根据下推标识状态枚举查询
     *
     * @param stateEnum 下推标识状态枚举
     * @return 下推标识列表
     */
    List<OrderPushDownIdentifierEntity> getByStateEnum(PushDownIdentifierStateEnum stateEnum);

    /**
     * 批量新增下推标识
     *
     * @param entities 下推标识实体列表
     */
    void batchAddIdentifier(List<OrderPushDownIdentifierEntity> entities);

    /**
     * 批量更新下推标识
     *
     * @param entities 下推标识实体列表
     */
    void batchUpdateIdentifier(List<OrderPushDownIdentifierEntity> entities);

    /**
     * 根据源单据类型和物料行id删除下推标识
     *
     * @param orderType 源单据类型
     * @param orderMaterialId 单据物料行id
     */
    void deleteByOrderTypeAndMaterialId(String orderType, String orderMaterialId);

    /**
     * 根据源单据类型和物料行id获取下推标识信息列表
     *
     * @param orderType 源单据类型
     * @param orderMaterialId 单据物料行id
     * @return 下推标识信息列表
     */
    List<PushDownIdentifierInfoDTO> getPushDownIdentifierInfos(String orderType, String orderMaterialId);

    /**
     * 批量获取下推标识信息列表
     *
     * @param orderType 源单据类型
     * @param orderMaterialIds 单据物料行id列表
     * @return 物料行id -> 下推标识信息列表的映射
     */
    Map<String, List<PushDownIdentifierInfoDTO>> batchGetPushDownIdentifierInfos(String orderType, List<String> orderMaterialIds);

    /**
     * 获取下推标识树形结构
     *
     * @param dto 全路径编码DTO
     * @return 下推标识树形结构列表
     */
    List<PushDownIdentifierTreeDTO> getPushDownIdentifierTree(PushDownFullPathCodeDTO dto);
}
