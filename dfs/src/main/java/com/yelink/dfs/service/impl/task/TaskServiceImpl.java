package com.yelink.dfs.service.impl.task;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.dfs.common.UserAuthenService;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.constant.task.TaskStateEnum;
import com.yelink.dfs.constant.task.TaskUserTypeEnum;
import com.yelink.dfs.entity.common.ExtApiConfig;
import com.yelink.dfs.entity.common.ExtRequestReq;
import com.yelink.dfs.entity.task.OrderCategoryEntity;
import com.yelink.dfs.entity.task.OrderRelationEntity;
import com.yelink.dfs.entity.task.TaskEntity;
import com.yelink.dfs.entity.task.TaskLogEntity;
import com.yelink.dfs.entity.task.TaskRelationEntity;
import com.yelink.dfs.entity.task.TaskRowEntity;
import com.yelink.dfs.entity.task.TaskUserConfigEntity;
import com.yelink.dfs.entity.task.dto.OrderTaskChartVO;
import com.yelink.dfs.entity.task.dto.OrderTaskOverviewVO;
import com.yelink.dfs.entity.task.dto.OrderTaskUpdateChartDTO;
import com.yelink.dfs.entity.task.dto.TaskDeleteDTO;
import com.yelink.dfs.entity.task.dto.TaskOverviewCountDTO;
import com.yelink.dfs.entity.task.dto.TaskPageDTO;
import com.yelink.dfs.entity.task.dto.TaskProgressDTO;
import com.yelink.dfs.entity.task.dto.TaskRelationTreeQueryDTO;
import com.yelink.dfs.entity.task.dto.TaskUpsertDTO;
import com.yelink.dfs.entity.task.dto.TaskUpsertRowDTO;
import com.yelink.dfs.entity.task.vo.TaskRelationTreeVO;
import com.yelink.dfs.entity.task.vo.TaskUserVO;
import com.yelink.dfs.event.PerMinute;
import com.yelink.dfs.mapper.task.TaskMapper;
import com.yelink.dfs.service.impl.common.WorkPropertise;
import com.yelink.dfs.service.impl.task.dto.TaskVO;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.task.OrderCategoryService;
import com.yelink.dfs.service.task.OrderRelationService;
import com.yelink.dfs.service.task.TaskLogService;
import com.yelink.dfs.service.task.TaskRelationService;
import com.yelink.dfs.service.task.TaskRoleConfigService;
import com.yelink.dfs.service.task.TaskRowService;
import com.yelink.dfs.service.task.TaskService;
import com.yelink.dfs.service.task.TaskUserConfigService;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfs.utils.ExtApiUtil;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfscommon.constant.dfs.task.TaskLogOperateEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.pojo.Result;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.ValidateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * @Description:
 * @Author: zengzhengfu
 * @Date: 2024/12/5
 */
@Slf4j
@Service
public class TaskServiceImpl extends ServiceImpl<TaskMapper, TaskEntity> implements TaskService {

    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private OrderCategoryService orderCategoryService;
    @Autowired
    private OrderRelationService orderRelationService;
    @Autowired
    private TaskUserConfigService taskUserConfigService;
    @Autowired
    private WorkPropertise workPropertise;
    @Resource
    private UserAuthenService userAuthenService;
    @Autowired
    private TaskRoleConfigService taskRoleConfigService;
    @Autowired
    private SysUserService userService;
    @Autowired
    private TaskLogService taskLogService;
    @Autowired
    private TaskRowService taskRowService;
    @Autowired
    private TaskRelationService taskRelationService;
    private static final String sdkProgressApi = "/task_sdk/progress";

    @Override
    public Page<TaskEntity> getPage(TaskPageDTO dto) {
        LambdaQueryChainWrapper<TaskEntity> wrapper = this.lambdaQuery();
        String username = userAuthenService.getUsername();
        // 关联 - 按角色
        if (dto.getFilterRelate() == TaskPageDTO.FilterRelate.ROLE) {
            List<String> orderCategories = taskRoleConfigService.curUserRoleOrderCategory(username);
            if (CollectionUtils.isEmpty(orderCategories)) {
                return new Page<>();
            }
            wrapper.in(TaskEntity::getOrderCategory, orderCategories);
        }
        // 关联 - 按关系
        if (dto.getFilterRelate() == TaskPageDTO.FilterRelate.RELATION) {
            // 如果没有选择用户类型，则返回空
            if (CollectionUtils.isEmpty(dto.getUserTypes())) {
                return new Page<>();
            }
            List<Integer> taskIds = taskUserConfigService.curUserTask(username, dto.getUserTypes());
            if (CollectionUtils.isEmpty(taskIds)) {
                return new Page<>();
            }
            wrapper.in(TaskEntity::getTaskId, taskIds);
        }
        Page<TaskEntity> page = wrapper
                .eq(StringUtils.isNotBlank(dto.getOrderCategory()), TaskEntity::getOrderCategory, dto.getOrderCategory())
                .eq(StringUtils.isNotBlank(dto.getOrderNumber()), TaskEntity::getOrderNumber, dto.getOrderNumber())
                .eq(StringUtils.isNotBlank(dto.getMaterialCode()), TaskEntity::getMaterialCode, dto.getMaterialCode())
                .eq(dto.getMaterialLineId() != null, TaskEntity::getMaterialLineId, dto.getMaterialLineId())
                .in(CollectionUtils.isNotEmpty(dto.getTaskStates()), TaskEntity::getTaskState, dto.getTaskStates())
                .eq(StringUtils.isNotBlank(dto.getUpstreamOrderCategory()), TaskEntity::getUpstreamOrderCategory, dto.getUpstreamOrderCategory())
                .eq(StringUtils.isNotBlank(dto.getUpstreamOrderNumber()), TaskEntity::getUpstreamOrderNumber, dto.getUpstreamOrderNumber())
                .eq(dto.getUpstreamMaterialLineId() != null, TaskEntity::getUpstreamMaterialLineId, dto.getUpstreamMaterialLineId())
                .eq(StringUtils.isNotBlank(dto.getOrderState()), TaskEntity::getOrderState, dto.getOrderState())
                .eq(StringUtils.isNotBlank(dto.getOrderStateName()), TaskEntity::getOrderStateName, dto.getOrderStateName())
                .eq(StringUtils.isNotBlank(dto.getRootOrderCategory()), TaskEntity::getRootOrderCategory, dto.getRootOrderCategory())
                .eq(StringUtils.isNotBlank(dto.getRootOrderNumber()), TaskEntity::getRootOrderNumber, dto.getRootOrderNumber())
                .eq(dto.getRootMaterialLineId() != null, TaskEntity::getRootMaterialLineId, dto.getRootMaterialLineId())
                .orderByDesc(TaskEntity::getCreateTime)
                .page(new Page<>(dto.getCurrent(), dto.getSize()));
        List<TaskEntity> records = page.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            List<Integer> taskIds = records.stream().map(TaskEntity::getTaskId).collect(Collectors.toList());
            // 展示名称
            showName(taskIds, records);
        }
        return page;
    }

    /**
     * 展示名称
     */
    private void showName(List<Integer> taskIds, List<TaskEntity> records) {
        MaterialService materialService = SpringUtil.getBean(MaterialService.class);
        Map<String, String> categoryCodeNameMap = orderCategoryService.getCodeNameMap(records.stream().map(TaskEntity::getOrderCategory).collect(Collectors.toList()));
        List<TaskUserConfigEntity> taskUserConfigEntities = taskUserConfigService.lambdaQuery().in(TaskUserConfigEntity::getTaskId, taskIds).list();
        List<String> usernames = taskUserConfigEntities.stream().map(TaskUserConfigEntity::getUsername).collect(Collectors.toList());
        Map<String, String> userNickMap = userService.getUserNameNickMap(usernames);
        Map<Integer, List<TaskUserConfigEntity>> userMap = taskUserConfigEntities.stream().collect(Collectors.groupingBy(TaskUserConfigEntity::getTaskId));
        // 物料信息
        List<String> materialCodes = records.stream().map(TaskEntity::getMaterialCode).distinct().collect(Collectors.toList());
        Map<String, String> materialCodeNameMap = materialService.getMaterialCodeNameMap(materialCodes);

        for (TaskEntity taskEntity : records) {
            taskEntity.setOrderCategoryName(categoryCodeNameMap.get(taskEntity.getOrderCategory()));
            taskEntity.setTaskStateName(TaskStateEnum.getNameByCode(taskEntity.getTaskState()));
            // 获取任务相关人员
            List<TaskUserConfigEntity> userConfigEntities = userMap.getOrDefault(taskEntity.getTaskId(), Collections.emptyList());
            List<TaskUserVO.UserVO> playerVOs = userConfigEntities.stream().filter(o -> o.getUserType().equals(TaskUserTypeEnum.PLAYER.getCode())).map(user -> TaskUserVO.UserVO.builder().username(user.getUsername()).nickname(userNickMap.get(user.getUsername())).build()).collect(Collectors.toList());
            List<TaskUserVO.UserVO> directorVOs = userConfigEntities.stream().filter(o -> o.getUserType().equals(TaskUserTypeEnum.DIRECTOR.getCode())).map(user -> TaskUserVO.UserVO.builder().username(user.getUsername()).nickname(userNickMap.get(user.getUsername())).build()).collect(Collectors.toList());
            taskEntity.setUserVO(TaskUserVO.builder().players(playerVOs).directors(directorVOs).build());
            taskEntity.setMaterialName(materialCodeNameMap.get(taskEntity.getMaterialCode()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void upsert(TaskUpsertDTO dto) {
        ValidateUtil.valid(dto, null);
        Date now = new Date();
        TaskEntity entity = JacksonUtil.convertObject(dto, TaskEntity.class);
        TaskEntity one = this.lambdaQuery()
                .eq(TaskEntity::getOrderCategory, entity.getOrderCategory())
                .eq(TaskEntity::getOrderNumber, entity.getOrderNumber())
                // 类似于销售订单这种才需要传递物料行id，将每一个物料行当作一次任务
                .eq(Objects.nonNull(entity.getMaterialLineId()), TaskEntity::getMaterialLineId, entity.getMaterialLineId())
                .one();
        List<TaskUpsertRowDTO> dtoRows = CollectionUtils.isEmpty(dto.getRows())? Collections.emptyList() :
                dto.getRows().stream().filter(e -> e.getMaterialLineId() != null && e.getMaterialCode() != null).collect(Collectors.toList());
        if (one == null) {
            //设置根信息
            setRootInfo(entity);
            entity.setCreateTime(now);
            this.save(entity);
            // 将任务相关人员新增到任务人员表
            setTaskUserConf(entity);
            // 任务行
            addRows(dtoRows, entity);
            taskLogService.save(
                    TaskLogEntity.builder().taskId(entity.getTaskId()).operateType(TaskLogOperateEnum.ADD).operator(dto.getOperator()).detail(dto.getLog()).operateTime(now).build()
            );
            return;
        }

        //更新逻辑
        entity.setTaskId(one.getTaskId());
        //上游单据改变
        boolean isChangeUpstreamOrderNumber = StringUtils.isNotBlank(entity.getUpstreamOrderNumber()) && !entity.getUpstreamOrderNumber().equals(one.getUpstreamOrderNumber());
        boolean isChangeUpstreamOrderMaterialLine = Objects.nonNull(entity.getUpstreamMaterialLineId()) && !entity.getUpstreamMaterialLineId().equals(one.getUpstreamMaterialLineId());
        if (isChangeUpstreamOrderNumber || isChangeUpstreamOrderMaterialLine) {
            setRootInfo(entity);
            //改变下游单据的根信息
            setDownStreamRootInfo(entity);
        }
        entity.setUpdateTime(now);
        this.updateById(entity);
        // 将任务相关人员新增到任务人员表
        setTaskUserConf(entity);
        // 任务行
        if(Boolean.TRUE.equals(dto.getRefreshAllRow())) {
            // 刷新所有
            taskRowService.lambdaUpdate().eq(TaskRowEntity::getTaskId, entity.getTaskId()).remove();
            addRows(dtoRows, entity);
        }else {
            if(CollectionUtils.isNotEmpty(dtoRows)) {
                // 局部刷新
                List<TaskRowEntity> oldRows = taskRowService.lambdaQuery().eq(TaskRowEntity::getTaskId, entity.getTaskId()).list();
                Map<Long, TaskRowEntity> oldMaterialLineIdRowMap = oldRows.stream().collect(Collectors.toMap(TaskRowEntity::getMaterialLineId, Function.identity()));
                // 插入不存在的
                addRows(dtoRows.stream().filter(e -> !oldMaterialLineIdRowMap.containsKey(e.getMaterialLineId())).collect(Collectors.toList()), entity);
                // 更新旧的
                List<TaskRowEntity> updateRows = dtoRows.stream().filter(e -> oldMaterialLineIdRowMap.containsKey(e.getMaterialLineId()))
                        .map(e -> {
                            TaskRowEntity r = oldMaterialLineIdRowMap.get(e.getMaterialLineId());
                            r.setMaterialCode(e.getMaterialCode());
                            r.setUpdateTime(now);
                            return r;
                        }).collect(Collectors.toList());
                taskRowService.updateBatchById(updateRows);
            }
        }
        taskLogService.save(
                TaskLogEntity.builder().taskId(entity.getTaskId()).operateType(TaskLogOperateEnum.UPDATE).operator(dto.getOperator()).detail(dto.getLog()).operateTime(now).build()
        );
    }

    private void addRows(Collection<TaskUpsertRowDTO> dtoRows, TaskEntity entity) {
        if(CollectionUtils.isNotEmpty(dtoRows)) {
            Date now = new Date();
            List<TaskRowEntity> rows = dtoRows.stream().map(e -> TaskRowEntity.builder()
                    .taskId(entity.getTaskId())
                    .materialCode(e.getMaterialCode())
                    .materialLineId(e.getMaterialLineId())
                    .createTime(now)
                    .build()
            ).collect(Collectors.toList());
            taskRowService.saveBatch(rows);
        }
    }

    /**
     * 将任务相关人员新增到任务人员表
     */
    private void setTaskUserConf(TaskEntity entity) {
        TaskUserVO userVO = entity.getUserVO();
        if (Objects.isNull(userVO)) {
            return;
        }
        // 参与人
        saveTaskUserConf(userVO.getPlayers(), entity.getTaskId(), TaskUserTypeEnum.PLAYER.getCode());
        // 负责人
        saveTaskUserConf(userVO.getDirectors(), entity.getTaskId(), TaskUserTypeEnum.DIRECTOR.getCode());
        // 后续其他人。。。
    }

    private void saveTaskUserConf(List<TaskUserVO.UserVO> userVOS, Integer taskId, String userType) {
        if (CollectionUtils.isEmpty(userVOS)) {
            return;
        }
        List<String> usernames = userVOS.stream().map(TaskUserVO.UserVO::getUsername).collect(Collectors.toList());
        // 过滤为空的数据
        usernames = usernames.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        // 获取历史用户，和新用户一起求全集
        List<TaskUserConfigEntity> historyUsers = taskUserConfigService.lambdaQuery()
                .eq(TaskUserConfigEntity::getTaskId, taskId)
                .eq(TaskUserConfigEntity::getUserType, userType)
                .list();
        List<String> historyUserNames = historyUsers.stream().map(TaskUserConfigEntity::getUsername).collect(Collectors.toList());
        historyUserNames.addAll(usernames);
        // 去重后新增
        taskUserConfigService.lambdaUpdate().eq(TaskUserConfigEntity::getTaskId, taskId)
                .eq(TaskUserConfigEntity::getUserType, userType).remove();
        List<TaskUserConfigEntity> directors = historyUserNames.stream().distinct().map(username -> TaskUserConfigEntity.builder()
                .taskId(taskId)
                .userType(userType)
                .username(username)
                .build()).collect(Collectors.toList());
        taskUserConfigService.saveBatch(directors);
    }

    /**
     * 设置下游根信息
     *
     * @param entity
     */
    private void setDownStreamRootInfo(TaskEntity entity) {
        //查询下游单据
        List<TaskEntity> list = this.lambdaQuery()
                .select(TaskEntity::getTaskId,
                        TaskEntity::getOrderCategory,
                        TaskEntity::getOrderNumber,
                        TaskEntity::getMaterialLineId)
                .eq(TaskEntity::getUpstreamOrderCategory, entity.getOrderCategory())
                .eq(TaskEntity::getUpstreamOrderNumber, entity.getOrderNumber())
                .eq(Objects.nonNull(entity.getMaterialLineId()), TaskEntity::getUpstreamMaterialLineId, entity.getMaterialLineId())
                .list();
        for (TaskEntity taskEntity : list) {
            taskEntity.setRootOrderCategory(entity.getRootOrderCategory());
            taskEntity.setRootOrderNumber(entity.getRootOrderNumber());
            taskEntity.setRootMaterialLineId(entity.getRootMaterialLineId());
            this.updateById(taskEntity);
            setDownStreamRootInfo(taskEntity);
        }
    }

    private void setRootInfo(TaskEntity entity) {
        if (StringUtils.isNotBlank(entity.getUpstreamOrderNumber())) {
            List<TaskEntity> upstreamOrders = this.lambdaQuery()
                    .eq(TaskEntity::getOrderCategory, entity.getUpstreamOrderCategory())
                    .eq(TaskEntity::getOrderNumber, entity.getUpstreamOrderNumber())
                    .eq(Objects.nonNull(entity.getUpstreamMaterialLineId()), TaskEntity::getMaterialLineId, entity.getUpstreamMaterialLineId())
                    .list();
            //查询上一级的根信息进行设置
            if (CollectionUtils.isNotEmpty(upstreamOrders)) {
                if(upstreamOrders.size() > 1) {
                    log.warn("警告, 上游单据存在多个! 上游信息: orderCategory:{},orderNumber:{},materialLineId:{}", entity.getUpstreamOrderCategory(), entity.getUpstreamOrderNumber(), entity.getUpstreamMaterialLineId());
                }
                TaskEntity upstreamOrder = upstreamOrders.get(0);
                entity.setRootOrderCategory(upstreamOrder.getRootOrderCategory());
                entity.setRootOrderNumber(upstreamOrder.getRootOrderNumber());
                entity.setRootMaterialLineId(upstreamOrder.getRootMaterialLineId());
            }
        } else {
            //没有上游单据，根设置为自己
            entity.setRootOrderCategory(entity.getOrderCategory());
            entity.setRootOrderNumber(entity.getOrderNumber());
            entity.setRootMaterialLineId(entity.getMaterialLineId());
        }
    }

    @Override
    public void delete(TaskDeleteDTO dto) {
        ValidateUtil.valid(dto, null);
        TaskEntity one = this.lambdaQuery()
                .eq(TaskEntity::getOrderCategory, dto.getOrderCategory())
                .eq(TaskEntity::getOrderNumber, dto.getOrderNumber())
                .eq(Objects.nonNull(dto.getMaterialLineId()), TaskEntity::getMaterialLineId, dto.getMaterialLineId())
                .one();
        if (one == null) {
            return;
        }
        //查询下级
        List<TaskEntity> list = this.lambdaQuery()
                .select(TaskEntity::getTaskId,
                        TaskEntity::getOrderCategory,
                        TaskEntity::getOrderNumber,
                        TaskEntity::getMaterialLineId)
                .eq(TaskEntity::getUpstreamOrderCategory, dto.getOrderCategory())
                .eq(TaskEntity::getUpstreamOrderNumber, dto.getOrderNumber())
                .eq(Objects.nonNull(dto.getMaterialLineId()), TaskEntity::getUpstreamMaterialLineId, dto.getMaterialLineId())
                .list();
        for (TaskEntity taskEntity : list) {
            //没有上游单据，根设置为自己
            taskEntity.setRootOrderCategory(taskEntity.getOrderCategory());
            taskEntity.setRootOrderNumber(taskEntity.getOrderNumber());
            taskEntity.setRootMaterialLineId(taskEntity.getMaterialLineId());
            //递归设置下级根信息
            setDownStreamRootInfo(taskEntity);
        }
        removeById(one);
        // 删除任务关联的用户表
        taskUserConfigService.lambdaUpdate().eq(TaskUserConfigEntity::getTaskId, one.getTaskId()).remove();
        taskRowService.lambdaUpdate().eq(TaskRowEntity::getTaskId, one.getTaskId()).remove();
        taskRelationService.lambdaUpdate().eq(TaskRelationEntity::getTaskId, one.getTaskId()).remove();
        taskLogService.lambdaUpdate().eq(TaskLogEntity::getTaskId, one.getTaskId()).remove();
    }

    @Override
    public OrderTaskChartVO getOrderTaskChart() {
        // 查询所有的单据大类
        List<OrderCategoryEntity> categoryEntities = orderCategoryService.list();
        // 查询所有的单据大类的任务链图表
        List<OrderRelationEntity> relationEntities = orderRelationService.list();
        Map<String, List<OrderRelationEntity>> downstreamOrderMap = relationEntities.stream().collect(Collectors.groupingBy(OrderRelationEntity::getUpstreamOrderCategory));
        Map<String, List<OrderRelationEntity>> upstreamOrderMap = relationEntities.stream().collect(Collectors.groupingBy(OrderRelationEntity::getDownstreamOrderCategory));
        List<OrderTaskChartVO.NodeLink> nodeLinks = new ArrayList<>();
        for (OrderCategoryEntity categoryEntity : categoryEntities) {
            // 该节点作为上级，查询对应下级的节点
            List<OrderRelationEntity> downstreamOrders = downstreamOrderMap.getOrDefault(categoryEntity.getOrderCategory(), new ArrayList<>());
            List<OrderTaskChartVO.NodeLink> downNodeLinks = downstreamOrders.stream().map(downstreamOrder ->
                    OrderTaskChartVO.NodeLink.builder()
                            .from(downstreamOrder.getUpstreamOrderCategory())
                            .to(downstreamOrder.getDownstreamOrderCategory())
                            .build()
            ).collect(Collectors.toList());
            nodeLinks.addAll(downNodeLinks);

            // 该节点作为下级，查询对应上级的节点
            List<OrderRelationEntity> upstreamOrders = upstreamOrderMap.getOrDefault(categoryEntity.getOrderCategory(), new ArrayList<>());
            List<OrderTaskChartVO.NodeLink> upNodeLinks = upstreamOrders.stream().map(upstreamOrder ->
                    OrderTaskChartVO.NodeLink.builder()
                            .from(upstreamOrder.getUpstreamOrderCategory())
                            .to(upstreamOrder.getDownstreamOrderCategory())
                            .build()
            ).collect(Collectors.toList());
            nodeLinks.addAll(upNodeLinks);
        }
        List<OrderTaskChartVO.NodeData> nodeData = categoryEntities.stream().map(categoryEntity ->
                        OrderTaskChartVO.NodeData.builder()
                                .id(categoryEntity.getOrderCategory())
                                .text(categoryEntity.getOrderName())
                                .fontColor(categoryEntity.getFontColor())
                                .color(categoryEntity.getBackgroundColor())
                                .x(categoryEntity.getLocationX())
                                .y(categoryEntity.getLocationY())
                                // 当前版本固定为启用
                                .data(OrderTaskChartVO.DataCondition.builder().enable(true).build())
                                .build())
                .collect(Collectors.toList());
        // 去重
        nodeLinks = nodeLinks.stream().distinct().collect(Collectors.toList());
        return OrderTaskChartVO.builder()
                .nodes(nodeData)
                .lines(nodeLinks)
                .build();
    }

    @Override
    public OrderTaskOverviewVO getTaskOverview(TaskPageDTO dto) {
        String username = userAuthenService.getUsername();
        // 处理角色过滤
        if (dto.getFilterRelate() == TaskPageDTO.FilterRelate.ROLE) {
            List<String> orderCategories = taskRoleConfigService.curUserRoleOrderCategory(username);
            if (CollectionUtils.isEmpty(orderCategories)) {
                return OrderTaskOverviewVO.builder().total(0).progressCount(0L).completeCount(0L).build();
            }
            // 如果已有orderCategory过滤条件，需要取交集
            if (StringUtils.isNotBlank(dto.getOrderCategory())) {
                if (!orderCategories.contains(dto.getOrderCategory())) {
                    return OrderTaskOverviewVO.builder().total(0).progressCount(0L).completeCount(0L).build();
                }
            } else {
                // 设置角色可见的单据类型列表
                dto.setRoleOrderCategories(orderCategories);
            }
        }
        // 处理关系过滤
        if (dto.getFilterRelate() == TaskPageDTO.FilterRelate.RELATION) {
            if (CollectionUtils.isEmpty(dto.getUserTypes())) {
                return OrderTaskOverviewVO.builder().total(0).progressCount(0L).completeCount(0L).build();
            }
            List<Integer> taskIds = taskUserConfigService.curUserTask(username, dto.getUserTypes());
            if (CollectionUtils.isEmpty(taskIds)) {
                return OrderTaskOverviewVO.builder().total(0).progressCount(0L).completeCount(0L).build();
            }
            dto.setRelationTaskIds(taskIds);
        }
        // 使用优化后的统计查询
        TaskOverviewCountDTO countResult = this.baseMapper.getTaskOverviewCount(dto);
        if (countResult == null) {
            return OrderTaskOverviewVO.builder().total(0).progressCount(0L).completeCount(0L).build();
        }
        return OrderTaskOverviewVO.builder()
                .total(countResult.getTotal())
                .progressCount(countResult.getProgressCount())
                .completeCount(countResult.getCompleteCount())
                .build();
    }

    @Override
    public void updateOrderTaskChart(List<OrderTaskUpdateChartDTO> updateChartDTOS) {
        for (OrderTaskUpdateChartDTO updateChartDTO : updateChartDTOS) {
            orderCategoryService.lambdaUpdate()
                    .eq(OrderCategoryEntity::getOrderCategory, updateChartDTO.getId())
                    .set(OrderCategoryEntity::getLocationX, updateChartDTO.getX())
                    .set(OrderCategoryEntity::getLocationY, updateChartDTO.getY())
                    .update();
        }
    }

    //@Override
    public void perMinuteTrigger(PerMinute event) {
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.TASK_PROCESS_LOCK, new Date(), 5, TimeUnit.MINUTES);
        if (lockStatus == null || !lockStatus) {
            return;
        }
        List<TaskEntity> list = this.lambdaQuery().eq(TaskEntity::getTaskState, TaskStateEnum.IN_PROGRESS.getCode()).list();
        Map<String, OrderCategoryEntity> orderCategoryMap = orderCategoryService.list().stream().collect(Collectors.toMap(OrderCategoryEntity::getOrderCategory, o -> o));

        Map<String, TaskEntity> taskMap = list.stream().collect(Collectors.toMap(o -> getUniqueKey(o), o -> o));
        //按单据类型分组，批量更新
        Map<String, List<TaskEntity>> collect = list.stream().collect(Collectors.groupingBy(TaskEntity::getOrderCategory));
        for (Map.Entry<String, List<TaskEntity>> entry : collect.entrySet()) {
            String orderCategory = entry.getKey();
            List<TaskEntity> taskEntities = entry.getValue();
            OrderCategoryEntity orderCategoryEntity = orderCategoryMap.get(orderCategory);
            if (orderCategoryEntity == null) {
                continue;
            }
            List<TaskProgressDTO> progressDTOS = taskEntities.stream().map(o -> TaskProgressDTO.builder()
                    .orderCategory(o.getOrderCategory())
                    .orderNumber(o.getOrderNumber())
                    .materialLineId(o.getMaterialLineId())
                    .build()).collect(Collectors.toList());

            ExtApiConfig build = getExtApiConfig(orderCategoryEntity);

            ExtRequestReq req = ExtRequestReq.builder().extApiConfig(build).body(progressDTOS).build();
            JSONObject res = ExtApiUtil.call(req);
            if (res == null) {
                continue;
            }
            List<TaskEntity> taskProgressResp = ExtApiUtil.generateResult(JSONArray.parseObject(res.toJSONString(), new TypeReference<Result<List<TaskEntity>>>() {
            }));

            List<TaskEntity> updateCollection = taskProgressResp.stream().map(o -> {
                TaskEntity db = taskMap.get(getUniqueKey(o));
                if (db == null) {
                    return null;
                }
                return TaskEntity.builder()
                        .taskId(db.getTaskId())
//                        .targetQuantity(o.getTargetQuantity())
//                        .progressQuantity(o.getProgressQuantity())
                        .build();
            }).filter(Objects::nonNull).collect(Collectors.toList());

            this.updateBatchById(updateCollection);
        }
    }

    private ExtApiConfig getExtApiConfig(OrderCategoryEntity orderCategoryEntity) {
        String requestPrefix = orderCategoryEntity.getRequestPrefix();
        String serviceUrl = workPropertise.getServiceUrl() + requestPrefix + sdkProgressApi;
        ExtApiConfig build = new ExtApiConfig();
        build.setService(requestPrefix);
        build.setModel("taskProgress");
        build.setInterfaceCode("getTaskProgress");
        build.setInterfaceName("查询任务进度");
        build.setHttpMethod(HttpMethod.POST.name());
        build.setPath(serviceUrl);
        return build;
    }

    private static String getUniqueKey(TaskEntity o) {
        return o.getOrderCategory() + Constant.UNDERLINE + o.getOrderNumber() + Constant.UNDERLINE + o.getMaterialLineId();
    }

    @Override
    public TaskVO taskTree(Integer taskId) {
        Map<String, String> categoryCodeNameMap = orderCategoryService.getCodeNameMap();
        TaskEntity task = this.getById(taskId);
        return getTaskVO(task, categoryCodeNameMap);
    }

    private TaskVO getTaskVO(TaskEntity task, Map<String, String> categoryCodeNameMap) {
        List<TaskEntity> sonTaskList = this.lambdaQuery()
                .eq(TaskEntity::getUpstreamOrderCategory, task.getOrderCategory())
                .eq(TaskEntity::getUpstreamOrderNumber, task.getOrderNumber())
                .eq(Objects.nonNull(task.getMaterialLineId()), TaskEntity::getUpstreamMaterialLineId, task.getMaterialLineId())
                .list();
        List<TaskVO> sonTaskVOList = new ArrayList<>();
        for (TaskEntity sonTask : sonTaskList) {
            TaskVO sonTaskVO = getTaskVO(sonTask, categoryCodeNameMap);
            sonTaskVOList.add(sonTaskVO);
        }
        TaskVO taskVO = JacksonUtil.convertObject(task, TaskVO.class);
        taskVO.setOrderCategoryName(categoryCodeNameMap.get(taskVO.getOrderCategory()));
        taskVO.setDownStreamTasks(sonTaskVOList);
        return taskVO;
    }

    @Override
    public List<TaskEntity> orderTrace(String orderCategory, String orderNumber) {
        List<TaskEntity> taskEntityList = this.lambdaQuery()
                .eq(TaskEntity::getOrderCategory, orderCategory)
                .eq(TaskEntity::getOrderNumber, orderNumber)
                .list();
        List<TaskEntity> result = new ArrayList<>();
        for (TaskEntity taskEntity : taskEntityList) {
            // 获取父级任务
            getParent(taskEntity, result);
            // 获取子级任务
            getChildren(taskEntity, result);
        }

        // 根据 taskId 去重
        return result.stream().collect(Collectors.collectingAndThen(
                Collectors.toMap(TaskEntity::getTaskId, task -> task, (existing, replacement) -> existing),
                map -> new ArrayList<>(map.values())
        ));
    }

    /**
     * 获取子级任务
     * @param task
     * @param result
     */
    private void getChildren(TaskEntity task, List<TaskEntity> result) {
        result.add(task);
        List<TaskEntity> sonTaskList = this.lambdaQuery()
                .eq(TaskEntity::getUpstreamOrderCategory, task.getOrderCategory())
                .eq(TaskEntity::getUpstreamOrderNumber, task.getOrderNumber())
                .eq(Objects.nonNull(task.getMaterialLineId()), TaskEntity::getUpstreamMaterialLineId, task.getMaterialLineId())
                .list();
        if (CollectionUtils.isEmpty(sonTaskList)) {
            return;
        }
        for (TaskEntity sonTask : sonTaskList) {
            getChildren(sonTask, result);
        }
    }

    /**
     * 获取父级任务
     * @param task
     * @param result
     */
    private void getParent(TaskEntity task, List<TaskEntity> result) {
        if (StringUtils.isBlank(task.getUpstreamOrderNumber())) {
            return;
        }
        List<TaskEntity> list = this.lambdaQuery()
                .eq(TaskEntity::getOrderCategory, task.getUpstreamOrderCategory())
                .eq(TaskEntity::getOrderNumber, task.getUpstreamOrderNumber())
                .eq(Objects.nonNull(task.getUpstreamMaterialLineId()), TaskEntity::getMaterialLineId, task.getUpstreamMaterialLineId())
                .list();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (TaskEntity parentTask : list) {
            // 递归获取父级任务
            getParent(parentTask, result);
            result.add(parentTask);
        }
    }

    @Override
    public TaskRelationTreeVO getTaskRelationTree(TaskRelationTreeQueryDTO dto) {
        // 根据单据类型、单据编号、物料行id查找任务
        List<TaskEntity> currentTasks = this.lambdaQuery()
                .eq(TaskEntity::getOrderCategory, dto.getOrderCategory())
                .eq(TaskEntity::getOrderNumber, dto.getOrderNumber())
                .eq(Objects.nonNull(dto.getMaterialLineId()), TaskEntity::getMaterialLineId, dto.getMaterialLineId())
                .list();

        if (CollectionUtils.isEmpty(currentTasks)) {
            throw new ResponseException("未找到对应的任务记录");
        }

        // 获取单据类型名称映射
        Map<String, String> categoryCodeNameMap = orderCategoryService.getCodeNameMap();

        // 如果只有一个任务，直接构建树
        if (currentTasks.size() == 1) {
            TaskEntity currentTask = currentTasks.get(0);
            return buildTaskRelationTree(currentTask, categoryCodeNameMap, 0, "current");
        } else {
            // 多个物料行的情况，创建虚拟根节点
            TaskRelationTreeVO virtualRoot = TaskRelationTreeVO.builder()
                    .orderCategory(dto.getOrderCategory())
                    .orderNumber(dto.getOrderNumber())
                    .nodeType("root")
                    .level(0)
                    .downstreamTasks(new ArrayList<>())
                    .build();

            for (TaskEntity task : currentTasks) {
                TaskRelationTreeVO taskTree = buildTaskRelationTree(task, categoryCodeNameMap, 0, "current");
                virtualRoot.getDownstreamTasks().add(taskTree);
            }
            return virtualRoot;
        }
    }

    /**
     * 构建任务关系树
     * @param task 当前任务
     * @param categoryCodeNameMap 单据类型名称映射
     * @param level 层级深度
     * @param nodeType 节点类型
     * @return 任务关系树VO
     */
    private TaskRelationTreeVO buildTaskRelationTree(TaskEntity task, Map<String, String> categoryCodeNameMap,
                                                   Integer level, String nodeType) {
        return buildTaskRelationTree(task, categoryCodeNameMap, level, nodeType, new HashSet<>());
    }

    /**
     * 构建任务关系树（带循环检测）
     * @param task 当前任务
     * @param categoryCodeNameMap 单据类型名称映射
     * @param level 层级深度
     * @param nodeType 节点类型
     * @param visitedTasks 已访问的任务集合，用于防止循环引用
     * @return 任务关系树VO
     */
    private TaskRelationTreeVO buildTaskRelationTree(TaskEntity task, Map<String, String> categoryCodeNameMap,
                                                   Integer level, String nodeType, Set<String> visitedTasks) {
        // 生成任务唯一标识：单据类型_单据编号_物料行ID
        String taskKey = generateTaskKey(task);

        // 检查是否已访问过此任务，防止循环引用
        if (visitedTasks.contains(taskKey)) {
            log.warn("检测到循环引用，任务: {}", taskKey);
            // 返回简化的节点信息，不再递归查询上下游
            TaskRelationTreeVO treeVO = convertToTreeVO(task, categoryCodeNameMap, level, nodeType);
            treeVO.setUpstreamTasks(new ArrayList<>());
            treeVO.setDownstreamTasks(new ArrayList<>());
            return treeVO;
        }

        // 将当前任务加入已访问集合
        visitedTasks.add(taskKey);

        TaskRelationTreeVO treeVO = convertToTreeVO(task, categoryCodeNameMap, level, nodeType);

        // 查询上游任务
        List<TaskRelationTreeVO> upstreamTasks = new ArrayList<>();
        if (StringUtils.isNotBlank(task.getUpstreamOrderNumber())) {
            List<TaskEntity> upstreamTaskList = this.lambdaQuery()
                    .eq(TaskEntity::getOrderCategory, task.getUpstreamOrderCategory())
                    .eq(TaskEntity::getOrderNumber, task.getUpstreamOrderNumber())
                    .eq(Objects.nonNull(task.getUpstreamMaterialLineId()), TaskEntity::getMaterialLineId, task.getUpstreamMaterialLineId())
                    .list();

            for (TaskEntity upstreamTask : upstreamTaskList) {
                // 创建新的访问集合副本，避免不同分支间的干扰
                Set<String> upstreamVisited = new HashSet<>(visitedTasks);
                TaskRelationTreeVO upstreamTreeVO = buildTaskRelationTree(upstreamTask, categoryCodeNameMap, level - 1, "upstream", upstreamVisited);
                upstreamTasks.add(upstreamTreeVO);
            }
        }

        // 查询下游任务
        List<TaskRelationTreeVO> downstreamTasks = new ArrayList<>();
        List<TaskEntity> downstreamTaskList = this.lambdaQuery()
                .eq(TaskEntity::getUpstreamOrderCategory, task.getOrderCategory())
                .eq(TaskEntity::getUpstreamOrderNumber, task.getOrderNumber())
                .eq(Objects.nonNull(task.getMaterialLineId()), TaskEntity::getUpstreamMaterialLineId, task.getMaterialLineId())
                .list();

        for (TaskEntity downstreamTask : downstreamTaskList) {
            // 创建新的访问集合副本，避免不同分支间的干扰
            Set<String> downstreamVisited = new HashSet<>(visitedTasks);
            TaskRelationTreeVO downstreamTreeVO = buildTaskRelationTree(downstreamTask, categoryCodeNameMap, level + 1, "downstream", downstreamVisited);
            downstreamTasks.add(downstreamTreeVO);
        }

        treeVO.setUpstreamTasks(upstreamTasks);
        treeVO.setDownstreamTasks(downstreamTasks);

        return treeVO;
    }

    /**
     * 生成任务唯一标识
     * @param task 任务实体
     * @return 任务唯一标识
     */
    private String generateTaskKey(TaskEntity task) {
        return task.getOrderCategory() + "_" + task.getOrderNumber() + "_" +
               (task.getMaterialLineId() != null ? task.getMaterialLineId() : "null");
    }

    /**
     * 将TaskEntity转换为TaskRelationTreeVO
     */
    private TaskRelationTreeVO convertToTreeVO(TaskEntity task, Map<String, String> categoryCodeNameMap, Integer level, String nodeType) {
        TaskRelationTreeVO relationTreeVO = JacksonUtil.convertObject(task, TaskRelationTreeVO.class);
        relationTreeVO.setOrderCategoryName(categoryCodeNameMap.get(task.getOrderCategory()));
        relationTreeVO.setNodeType(nodeType);
        relationTreeVO.setLevel(level);
        return relationTreeVO;
    }
}

