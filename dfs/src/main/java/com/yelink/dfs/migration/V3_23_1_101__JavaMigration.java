package com.yelink.dfs.migration;

import com.yelink.dfs.service.common.config.OrderPushDownIdentifierService;
import com.yelink.dfs.service.common.config.OrderPushDownRecordService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownIdentifierStateEnum;
import com.yelink.dfscommon.entity.dfs.OrderPushDownIdentifierEntity;
import com.yelink.dfscommon.entity.dfs.OrderPushDownRecordEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 数据迁移：从OrderPushDownRecordEntity迁移数据到OrderPushDownIdentifierEntity
 *
 * <AUTHOR>
 */
@Slf4j
public class V3_23_1_101__JavaMigration extends BaseJavaMigration {

    @Override
    public void migrate(Context context) {
        try {
            log.info("开始迁移下推标识数据");
            // 迁移数据
            migrateData();
            log.info("数据迁移完成");
        } catch (Exception e) {
            log.error("数据迁移失败", e);
            throw new RuntimeException("数据迁移失败", e);
        }
    }

    /**
     * 执行数据迁移
     */
    private void migrateData() {
        OrderPushDownRecordService pushDownRecordService = SpringUtil.getBean(OrderPushDownRecordService.class);
        OrderPushDownIdentifierService pushDownIdentifierService = SpringUtil.getBean(OrderPushDownIdentifierService.class);
        // 直接查询生产工单用料清单相关的下推记录数据
        // 注意：这里的sourceOrderMaterialId应该对应WorkOrderMaterialListMaterialEntity的id
        List<OrderPushDownRecordEntity> recordList = pushDownRecordService.lambdaQuery()
            .eq(OrderPushDownRecordEntity::getSourceOrderType, "workOrderMaterialList")
            .isNotNull(OrderPushDownRecordEntity::getSourceOrderMaterialId)
            .list();

        if (recordList.isEmpty()) {
            log.info("没有需要迁移的生产工单用料清单下推记录数据");
            return;
        }

        log.info("找到 {} 条生产工单用料清单下推记录数据", recordList.size());

        Map<String, List<OrderPushDownRecordEntity>> groupedRecords = recordList.stream()
            .collect(Collectors.groupingBy(this::buildGroupKey));

        log.info("分组后得到 {} 个唯一的标识组合需要迁移", groupedRecords.size());

        if (groupedRecords.isEmpty()) {
            log.info("没有生产工单用料清单相关的记录需要迁移");
            return;
        }
        List<OrderPushDownIdentifierEntity> identifierList = new ArrayList<>();
        int processedCount = 0;
        int skippedCount = 0;
        // 处理每个分组
        for (Map.Entry<String, List<OrderPushDownRecordEntity>> entry : groupedRecords.entrySet()) {
            try {
                List<OrderPushDownRecordEntity> records = entry.getValue();
                OrderPushDownRecordEntity firstRecord = records.get(0);
                // 检查是否已存在相同的标识记录
                OrderPushDownIdentifierEntity existingIdentifier = pushDownIdentifierService.getByOrderTypeAndMaterialIdAndBatch(
                    firstRecord.getSourceOrderType(),
                    String.valueOf(firstRecord.getSourceOrderMaterialId()),
                    firstRecord.getSourceOrderBatch()
                );
                if (existingIdentifier != null) {
                    skippedCount++;
                    continue;
                }
                // 创建新的标识记录
                OrderPushDownIdentifierEntity identifier = createIdentifierFromRecord(firstRecord, records);
                identifierList.add(identifier);
                processedCount++;
                // 分批处理，避免内存溢出
                if (identifierList.size() >= 1000) {
                    pushDownIdentifierService.batchAddIdentifier(identifierList);
                    identifierList.clear();
                }
            } catch (Exception e) {
                log.error("处理分组 {} 时发生错误", entry.getKey(), e);
                // 继续处理其他记录，不中断整个迁移过程
            }
        }

        // 处理剩余的记录
        if (!identifierList.isEmpty()) {
            pushDownIdentifierService.batchAddIdentifier(identifierList);
        }
        log.info("数据迁移完成：处理 {} 条，跳过 {} 条", processedCount, skippedCount);
    }

    /**
     * 构建分组键
     */
    private String buildGroupKey(OrderPushDownRecordEntity record) {
        String batchNumber = StringUtils.isBlank(record.getSourceOrderBatch()) ? "NULL" : record.getSourceOrderBatch();
        return record.getSourceOrderType() + "_" + record.getSourceOrderMaterialId() + "_" + batchNumber;
    }

    /**
     * 从下推记录创建标识记录
     */
    private OrderPushDownIdentifierEntity createIdentifierFromRecord(OrderPushDownRecordEntity firstRecord,
                                                                    List<OrderPushDownRecordEntity> allRecords) {
        OrderPushDownIdentifierEntity identifier = new OrderPushDownIdentifierEntity();
        // 设置基本字段
        identifier.setOrderType(firstRecord.getSourceOrderType());
        identifier.setOrderMaterialId(String.valueOf(firstRecord.getSourceOrderMaterialId()));
        identifier.setBatchNumber(firstRecord.getSourceOrderBatch());
        identifier.setTargetOrderType(firstRecord.getTargetOrderType());
        // 计算下推状态
        String state = calculatePushDownState(allRecords);
        identifier.setState(state);
        return identifier;
    }

    /**
     * 根据下推记录计算下推状态
     */
    private String calculatePushDownState(List<OrderPushDownRecordEntity> records) {
        if (records == null || records.isEmpty()) {
            return PushDownIdentifierStateEnum.NO_PUSH_DOWN.getCode();
        }
        // 统计有目标单据的记录数量
        long successfulRecords = records.stream()
            .filter(record -> StringUtils.isNotBlank(record.getTargetOrderNumber()))
            .filter(record -> record.getIsAbnormal() == null || !record.getIsAbnormal())
            .count();
        // 如果没有成功的下推记录，认为是未下推
        if (successfulRecords == 0) {
            return PushDownIdentifierStateEnum.NO_PUSH_DOWN.getCode();
        }
        // 如果所有记录都成功下推，认为是已下推
        if (successfulRecords == records.size()) {
            return PushDownIdentifierStateEnum.ALL_PUSH_DOWN.getCode();
        }
        // 否则认为是部分下推
        return PushDownIdentifierStateEnum.PART_PUSH_DOWN.getCode();
    }
}

