package com.yelink.dfs.open.v1.task;

import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.task.dto.TaskDeleteDTO;
import com.yelink.dfs.entity.task.dto.TaskRelationDeleteDTO;
import com.yelink.dfs.entity.task.dto.TaskRelationTreeQueryDTO;
import com.yelink.dfs.entity.task.dto.TaskRelationUpsertDTO;
import com.yelink.dfs.entity.task.dto.TaskUpsertDTO;
import com.yelink.dfs.entity.task.vo.TaskRelationTreeVO;
import com.yelink.dfs.service.task.TaskRelationService;
import com.yelink.dfs.service.task.TaskService;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.pojo.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/v1/open/task")
@Api(tags = "任务中心/V1/任务操作")
public class TaskOpenController extends BaseController {

    private TaskService taskService;
    private TaskRelationService taskRelationService;

    /**
     * 新增/更新任务
     */
    @ApiOperation(value = "新增/更新任务", httpMethod = "POST")
    @PostMapping("/upsert")
    public Result<?> upsertTask(@RequestBody @Validated TaskUpsertDTO dto, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        taskService.upsert(dto);
        return Result.success();
    }

    /**
     * 删除任务
     */
    @ApiOperation(value = "删除任务", httpMethod = "POST")
    @PostMapping("/delete")
    public Result<?> deleteTask(@RequestBody @Validated TaskDeleteDTO dto) {
        taskService.delete(dto);
        return Result.success();
    }

    /**
     * 新增/更新任务关联项
     */
    @ApiOperation(value = "新增/更新任务关联项", httpMethod = "POST")
    @PostMapping("/upsert_relation")
    public Result<?> upsertTaskRelation(@RequestBody @Validated TaskRelationUpsertDTO dto, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        taskRelationService.upsert(dto);
        return Result.success();
    }

    /**
     * 删除任务关联项
     */
    @ApiOperation(value = "删除任务关联项", httpMethod = "POST")
    @PostMapping("/delete_relation")
    public Result<?> deleteTaskRelation(@RequestBody @Validated TaskRelationDeleteDTO dto) {
        taskRelationService.delete(dto);
        return Result.success();
    }

    /**
     * 查询任务关系树
     * 根据单据类型、单据编号、物料行id查询完整的上下游关系树
     */
    @ApiOperation(value = "查询任务关系树", httpMethod = "POST")
    @PostMapping("/relation_tree")
    public Result<List<TaskRelationTreeVO>> queryTaskRelationTree(@RequestBody @Validated TaskRelationTreeQueryDTO dto, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        List<TaskRelationTreeVO> result = taskService.getTaskRelationTree(dto);
        return Result.success(result);
    }
}
