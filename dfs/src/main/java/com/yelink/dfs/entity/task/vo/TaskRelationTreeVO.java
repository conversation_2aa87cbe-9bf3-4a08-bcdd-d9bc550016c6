package com.yelink.dfs.entity.task.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yelink.dfs.constant.task.TaskStateEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;
import java.util.List;


/**
 * 任务关系树VO
 * <AUTHOR>
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class TaskRelationTreeVO {

    /**
     * 任务id
     */
    @ApiModelProperty("任务id")
    private Integer taskId;

    /**
     * 单据类型
     */
    @ApiModelProperty("单据类型")
    private String orderCategory;

    /**
     * 单据类型名称
     */
    @ApiModelProperty("单据类型名称")
    private String orderCategoryName;

    /**
     * 单据编号
     */
    @ApiModelProperty("单据编号")
    private String orderNumber;

    /**
     * 单据表主键
     */
    @ApiModelProperty("单据表主键")
    private String orderId;

    /**
     * 物料编码
     */
    @ApiModelProperty("物料编码")
    private String materialCode;

    /**
     * 物料名称
     */
    @ApiModelProperty("物料名称")
    private String materialName;

    /**
     * 物料行id
     */
    @ApiModelProperty("物料行id")
    private Long materialLineId;

    /**
     * 单据状态编码
     */
    @ApiModelProperty("单据状态编码")
    private String orderState;

    /**
     * 单据状态名称
     */
    @ApiModelProperty("单据状态名称")
    private String orderStateName;

    /**
     * 任务状态
     */
    @ApiModelProperty("任务状态")
    private String taskState;

    /**
     * 任务状态名称
     */
    @ApiModelProperty("任务状态名称")
    private String taskStateName;

    /**
     * 获取任务状态名称
     */
    public String getTaskStateName() {
        return TaskStateEnum.getNameByCode(taskState);
    }

    /**
     * 任务进度
     */
    @ApiModelProperty("任务进度")
    private Double taskProgress;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 上游任务列表
     */
    @ApiModelProperty("上游任务列表")
    private List<TaskRelationTreeVO> upstreamTasks;

    /**
     * 下游任务列表
     */
    @ApiModelProperty("下游任务列表")
    private List<TaskRelationTreeVO> downstreamTasks;

    /**
     * 节点类型：root-根节点，current-当前节点，upstream-上游节点，downstream-下游节点
     */
    @ApiModelProperty("节点类型")
    private String nodeType;

    /**
     * 层级深度，当前节点为0，上游为负数，下游为正数
     */
    @ApiModelProperty("层级深度")
    private Integer level;

    /**
     * 兄弟任务列表（用于处理同一单据下多个物料行的情况）
     */
    @ApiModelProperty("兄弟任务列表")
    private List<TaskRelationTreeVO> siblingTasks;

}
